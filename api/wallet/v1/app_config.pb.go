// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/wallet/v1/app_config.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetAppConfigReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAppConfigReq) Reset() {
	*x = GetAppConfigReq{}
	mi := &file_api_wallet_v1_app_config_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAppConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppConfigReq) ProtoMessage() {}

func (x *GetAppConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_app_config_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppConfigReq.ProtoReflect.Descriptor instead.
func (*GetAppConfigReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_app_config_proto_rawDescGZIP(), []int{0}
}

type GetAppConfigReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Endpoints     string                 `protobuf:"bytes,1,opt,name=endpoints,proto3" json:"endpoints,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAppConfigReply) Reset() {
	*x = GetAppConfigReply{}
	mi := &file_api_wallet_v1_app_config_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAppConfigReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppConfigReply) ProtoMessage() {}

func (x *GetAppConfigReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_app_config_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppConfigReply.ProtoReflect.Descriptor instead.
func (*GetAppConfigReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_app_config_proto_rawDescGZIP(), []int{1}
}

func (x *GetAppConfigReply) GetEndpoints() string {
	if x != nil {
		return x.Endpoints
	}
	return ""
}

var File_api_wallet_v1_app_config_proto protoreflect.FileDescriptor

const file_api_wallet_v1_app_config_proto_rawDesc = "" +
	"\n" +
	"\x1eapi/wallet/v1/app_config.proto\x12\rapi.wallet.v1\x1a\x1cgoogle/api/annotations.proto\"\x11\n" +
	"\x0fGetAppConfigReq\"1\n" +
	"\x11GetAppConfigReply\x12\x1c\n" +
	"\tendpoints\x18\x01 \x01(\tR\tendpoints2|\n" +
	"\x10AppConfigService\x12h\n" +
	"\fGetAppConfig\x12\x1e.api.wallet.v1.GetAppConfigReq\x1a .api.wallet.v1.GetAppConfigReply\"\x16\x82\xd3\xe4\x93\x02\x10\x12\x0e/v1/app/configB\x96\x01\n" +
	"\x11com.api.wallet.v1B\x0eAppConfigProtoP\x01Z\x1bbyd_wallet/api/wallet/v1;v1\xa2\x02\x03AWX\xaa\x02\rApi.Wallet.V1\xca\x02\rApi\\Wallet\\V1\xe2\x02\x19Api\\Wallet\\V1\\GPBMetadata\xea\x02\x0fApi::Wallet::V1b\x06proto3"

var (
	file_api_wallet_v1_app_config_proto_rawDescOnce sync.Once
	file_api_wallet_v1_app_config_proto_rawDescData []byte
)

func file_api_wallet_v1_app_config_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_app_config_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_app_config_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_wallet_v1_app_config_proto_rawDesc), len(file_api_wallet_v1_app_config_proto_rawDesc)))
	})
	return file_api_wallet_v1_app_config_proto_rawDescData
}

var file_api_wallet_v1_app_config_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_wallet_v1_app_config_proto_goTypes = []any{
	(*GetAppConfigReq)(nil),   // 0: api.wallet.v1.GetAppConfigReq
	(*GetAppConfigReply)(nil), // 1: api.wallet.v1.GetAppConfigReply
}
var file_api_wallet_v1_app_config_proto_depIdxs = []int32{
	0, // 0: api.wallet.v1.AppConfigService.GetAppConfig:input_type -> api.wallet.v1.GetAppConfigReq
	1, // 1: api.wallet.v1.AppConfigService.GetAppConfig:output_type -> api.wallet.v1.GetAppConfigReply
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_app_config_proto_init() }
func file_api_wallet_v1_app_config_proto_init() {
	if File_api_wallet_v1_app_config_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_wallet_v1_app_config_proto_rawDesc), len(file_api_wallet_v1_app_config_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_app_config_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_app_config_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_app_config_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_app_config_proto = out.File
	file_api_wallet_v1_app_config_proto_goTypes = nil
	file_api_wallet_v1_app_config_proto_depIdxs = nil
}
