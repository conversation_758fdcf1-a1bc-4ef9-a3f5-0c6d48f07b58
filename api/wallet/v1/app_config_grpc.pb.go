// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/wallet/v1/app_config.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AppConfigService_GetAppConfig_FullMethodName = "/api.wallet.v1.AppConfigService/GetAppConfig"
)

// AppConfigServiceClient is the client API for AppConfigService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppConfigServiceClient interface {
	// 获取app配置信息
	GetAppConfig(ctx context.Context, in *GetAppConfigReq, opts ...grpc.CallOption) (*GetAppConfigReply, error)
}

type appConfigServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAppConfigServiceClient(cc grpc.ClientConnInterface) AppConfigServiceClient {
	return &appConfigServiceClient{cc}
}

func (c *appConfigServiceClient) GetAppConfig(ctx context.Context, in *GetAppConfigReq, opts ...grpc.CallOption) (*GetAppConfigReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAppConfigReply)
	err := c.cc.Invoke(ctx, AppConfigService_GetAppConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppConfigServiceServer is the server API for AppConfigService service.
// All implementations must embed UnimplementedAppConfigServiceServer
// for forward compatibility.
type AppConfigServiceServer interface {
	// 获取app配置信息
	GetAppConfig(context.Context, *GetAppConfigReq) (*GetAppConfigReply, error)
	mustEmbedUnimplementedAppConfigServiceServer()
}

// UnimplementedAppConfigServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAppConfigServiceServer struct{}

func (UnimplementedAppConfigServiceServer) GetAppConfig(context.Context, *GetAppConfigReq) (*GetAppConfigReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppConfig not implemented")
}
func (UnimplementedAppConfigServiceServer) mustEmbedUnimplementedAppConfigServiceServer() {}
func (UnimplementedAppConfigServiceServer) testEmbeddedByValue()                          {}

// UnsafeAppConfigServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppConfigServiceServer will
// result in compilation errors.
type UnsafeAppConfigServiceServer interface {
	mustEmbedUnimplementedAppConfigServiceServer()
}

func RegisterAppConfigServiceServer(s grpc.ServiceRegistrar, srv AppConfigServiceServer) {
	// If the following call pancis, it indicates UnimplementedAppConfigServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AppConfigService_ServiceDesc, srv)
}

func _AppConfigService_GetAppConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppConfigServiceServer).GetAppConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppConfigService_GetAppConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppConfigServiceServer).GetAppConfig(ctx, req.(*GetAppConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AppConfigService_ServiceDesc is the grpc.ServiceDesc for AppConfigService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppConfigService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.wallet.v1.AppConfigService",
	HandlerType: (*AppConfigServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAppConfig",
			Handler:    _AppConfigService_GetAppConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/wallet/v1/app_config.proto",
}
