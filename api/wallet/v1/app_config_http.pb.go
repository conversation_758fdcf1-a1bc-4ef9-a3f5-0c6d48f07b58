// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: api/wallet/v1/app_config.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAppConfigServiceGetAppConfig = "/api.wallet.v1.AppConfigService/GetAppConfig"

type AppConfigServiceHTTPServer interface {
	// GetAppConfig 获取app配置信息
	GetAppConfig(context.Context, *GetAppConfigReq) (*GetAppConfigReply, error)
}

func RegisterAppConfigServiceHTTPServer(s *http.Server, srv AppConfigServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/app/config", _AppConfigService_GetAppConfig0_HTTP_Handler(srv))
}

func _AppConfigService_GetAppConfig0_HTTP_Handler(srv AppConfigServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAppConfigReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAppConfigServiceGetAppConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAppConfig(ctx, req.(*GetAppConfigReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAppConfigReply)
		return ctx.Result(200, reply)
	}
}

type AppConfigServiceHTTPClient interface {
	GetAppConfig(ctx context.Context, req *GetAppConfigReq, opts ...http.CallOption) (rsp *GetAppConfigReply, err error)
}

type AppConfigServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewAppConfigServiceHTTPClient(client *http.Client) AppConfigServiceHTTPClient {
	return &AppConfigServiceHTTPClientImpl{client}
}

func (c *AppConfigServiceHTTPClientImpl) GetAppConfig(ctx context.Context, in *GetAppConfigReq, opts ...http.CallOption) (*GetAppConfigReply, error) {
	var out GetAppConfigReply
	pattern := "/v1/app/config"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAppConfigServiceGetAppConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
