// Code generated by protoc-gen-go-errors. DO NOT EDIT.

package v1

import (
	fmt "fmt"
	errors "github.com/go-kratos/kratos/v2/errors"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
const _ = errors.SupportPackageIsVersion1

// 内部服务错误
func IsInternalServer(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_INTERNAL_SERVER.String() && e.Code == 100
}

// 内部服务错误
func ErrorInternalServer(format string, args ...interface{}) *errors.Error {
	return errors.New(100, ErrorReason_INTERNAL_SERVER.String(), fmt.Sprintf(format, args...))
}

// 参数校验错误
func IsValidator(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_VALIDATOR.String() && e.Code == 101
}

// 参数校验错误
func ErrorValidator(format string, args ...interface{}) *errors.Error {
	return errors.New(101, ErrorReason_VALIDATOR.String(), fmt.Sprintf(format, args...))
}

// 波场租赁错误
func IsRent(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_RENT.String() && e.Code == 102
}

// 波场租赁错误
func ErrorRent(format string, args ...interface{}) *errors.Error {
	return errors.New(102, ErrorReason_RENT.String(), fmt.Sprintf(format, args...))
}

// boss wallet  user 相关
func IsBossidLock(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_BOSSID_LOCK.String() && e.Code == 103
}

// boss wallet  user 相关
func ErrorBossidLock(format string, args ...interface{}) *errors.Error {
	return errors.New(103, ErrorReason_BOSSID_LOCK.String(), fmt.Sprintf(format, args...))
}

func IsBossidExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_BOSSID_EXISTS.String() && e.Code == 104
}

func ErrorBossidExists(format string, args ...interface{}) *errors.Error {
	return errors.New(104, ErrorReason_BOSSID_EXISTS.String(), fmt.Sprintf(format, args...))
}

// 数据已存在
func IsDataExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_DATA_EXISTS.String() && e.Code == 105
}

// 数据已存在
func ErrorDataExists(format string, args ...interface{}) *errors.Error {
	return errors.New(105, ErrorReason_DATA_EXISTS.String(), fmt.Sprintf(format, args...))
}

// 兑换券兑换失败
func IsVoucherRedeem(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_VOUCHER_REDEEM.String() && e.Code == 106
}

// 兑换券兑换失败
func ErrorVoucherRedeem(format string, args ...interface{}) *errors.Error {
	return errors.New(106, ErrorReason_VOUCHER_REDEEM.String(), fmt.Sprintf(format, args...))
}

// 券码匹配失败
func IsVoucherMatch(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_VOUCHER_MATCH.String() && e.Code == 107
}

// 券码匹配失败
func ErrorVoucherMatch(format string, args ...interface{}) *errors.Error {
	return errors.New(107, ErrorReason_VOUCHER_MATCH.String(), fmt.Sprintf(format, args...))
}
