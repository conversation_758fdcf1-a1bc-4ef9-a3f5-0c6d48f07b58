// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/wallet/v1/user_guide.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListUserGuideCategoriesReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserGuideCategoriesReq) Reset() {
	*x = ListUserGuideCategoriesReq{}
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserGuideCategoriesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserGuideCategoriesReq) ProtoMessage() {}

func (x *ListUserGuideCategoriesReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserGuideCategoriesReq.ProtoReflect.Descriptor instead.
func (*ListUserGuideCategoriesReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_guide_proto_rawDescGZIP(), []int{0}
}

type ListUserGuideCategoriesReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类列表
	List          []*UserGuideCategoryInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserGuideCategoriesReply) Reset() {
	*x = ListUserGuideCategoriesReply{}
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserGuideCategoriesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserGuideCategoriesReply) ProtoMessage() {}

func (x *ListUserGuideCategoriesReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserGuideCategoriesReply.ProtoReflect.Descriptor instead.
func (*ListUserGuideCategoriesReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_guide_proto_rawDescGZIP(), []int{1}
}

func (x *ListUserGuideCategoriesReply) GetList() []*UserGuideCategoryInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type ListUserGuidesReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类ID
	CategoryId    uint64 `protobuf:"varint,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserGuidesReq) Reset() {
	*x = ListUserGuidesReq{}
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserGuidesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserGuidesReq) ProtoMessage() {}

func (x *ListUserGuidesReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserGuidesReq.ProtoReflect.Descriptor instead.
func (*ListUserGuidesReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_guide_proto_rawDescGZIP(), []int{2}
}

func (x *ListUserGuidesReq) GetCategoryId() uint64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

type ListUserGuidesReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户指南列表
	List          []*UserGuideInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserGuidesReply) Reset() {
	*x = ListUserGuidesReply{}
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserGuidesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserGuidesReply) ProtoMessage() {}

func (x *ListUserGuidesReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserGuidesReply.ProtoReflect.Descriptor instead.
func (*ListUserGuidesReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_guide_proto_rawDescGZIP(), []int{3}
}

func (x *ListUserGuidesReply) GetList() []*UserGuideInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type ListUserGuideContentReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户指南ID
	Id            uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserGuideContentReq) Reset() {
	*x = ListUserGuideContentReq{}
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserGuideContentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserGuideContentReq) ProtoMessage() {}

func (x *ListUserGuideContentReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserGuideContentReq.ProtoReflect.Descriptor instead.
func (*ListUserGuideContentReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_guide_proto_rawDescGZIP(), []int{4}
}

func (x *ListUserGuideContentReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ListUserGuideContentReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 内容列表
	List          []*UserGuideContentInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserGuideContentReply) Reset() {
	*x = ListUserGuideContentReply{}
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserGuideContentReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserGuideContentReply) ProtoMessage() {}

func (x *ListUserGuideContentReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserGuideContentReply.ProtoReflect.Descriptor instead.
func (*ListUserGuideContentReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_guide_proto_rawDescGZIP(), []int{5}
}

func (x *ListUserGuideContentReply) GetList() []*UserGuideContentInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type UserGuideCategoryInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 语言
	Language string `protobuf:"bytes,2,opt,name=language,proto3" json:"language,omitempty"`
	// 分类名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 图标URL
	LogoUrl       string `protobuf:"bytes,4,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserGuideCategoryInfo) Reset() {
	*x = UserGuideCategoryInfo{}
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserGuideCategoryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserGuideCategoryInfo) ProtoMessage() {}

func (x *UserGuideCategoryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserGuideCategoryInfo.ProtoReflect.Descriptor instead.
func (*UserGuideCategoryInfo) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_guide_proto_rawDescGZIP(), []int{6}
}

func (x *UserGuideCategoryInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserGuideCategoryInfo) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *UserGuideCategoryInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserGuideCategoryInfo) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

type UserGuideInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户指南ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 分类ID
	CategoryId uint64 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 语言
	Language string `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`
	// 标题
	Title string `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	// 简介
	Summary       string `protobuf:"bytes,5,opt,name=summary,proto3" json:"summary,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserGuideInfo) Reset() {
	*x = UserGuideInfo{}
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserGuideInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserGuideInfo) ProtoMessage() {}

func (x *UserGuideInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserGuideInfo.ProtoReflect.Descriptor instead.
func (*UserGuideInfo) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_guide_proto_rawDescGZIP(), []int{7}
}

func (x *UserGuideInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserGuideInfo) GetCategoryId() uint64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *UserGuideInfo) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *UserGuideInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UserGuideInfo) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

type UserGuideBasicInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户指南ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 分类ID
	CategoryId uint64 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 分类信息
	Category *UserGuideCategoryInfo `protobuf:"bytes,3,opt,name=category,proto3" json:"category,omitempty"`
	// 语言
	Language string `protobuf:"bytes,4,opt,name=language,proto3" json:"language,omitempty"`
	// 标题
	Title string `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	// 简介
	Summary string `protobuf:"bytes,6,opt,name=summary,proto3" json:"summary,omitempty"`
	// 创建时间
	CreatedAt string `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt     string `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserGuideBasicInfo) Reset() {
	*x = UserGuideBasicInfo{}
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserGuideBasicInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserGuideBasicInfo) ProtoMessage() {}

func (x *UserGuideBasicInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserGuideBasicInfo.ProtoReflect.Descriptor instead.
func (*UserGuideBasicInfo) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_guide_proto_rawDescGZIP(), []int{8}
}

func (x *UserGuideBasicInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserGuideBasicInfo) GetCategoryId() uint64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *UserGuideBasicInfo) GetCategory() *UserGuideCategoryInfo {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *UserGuideBasicInfo) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *UserGuideBasicInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UserGuideBasicInfo) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *UserGuideBasicInfo) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *UserGuideBasicInfo) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

type UserGuideContentInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 内容ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 正文文本
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	// 图片URL
	PhotoUrl      string `protobuf:"bytes,3,opt,name=photo_url,json=photoUrl,proto3" json:"photo_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserGuideContentInfo) Reset() {
	*x = UserGuideContentInfo{}
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserGuideContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserGuideContentInfo) ProtoMessage() {}

func (x *UserGuideContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserGuideContentInfo.ProtoReflect.Descriptor instead.
func (*UserGuideContentInfo) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_guide_proto_rawDescGZIP(), []int{9}
}

func (x *UserGuideContentInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserGuideContentInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *UserGuideContentInfo) GetPhotoUrl() string {
	if x != nil {
		return x.PhotoUrl
	}
	return ""
}

type SearchUserGuidesReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 搜索关键词
	Keyword       string `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchUserGuidesReq) Reset() {
	*x = SearchUserGuidesReq{}
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchUserGuidesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUserGuidesReq) ProtoMessage() {}

func (x *SearchUserGuidesReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUserGuidesReq.ProtoReflect.Descriptor instead.
func (*SearchUserGuidesReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_guide_proto_rawDescGZIP(), []int{10}
}

func (x *SearchUserGuidesReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

type SearchUserGuidesReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户指南列表
	List          []*UserGuideInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchUserGuidesReply) Reset() {
	*x = SearchUserGuidesReply{}
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchUserGuidesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUserGuidesReply) ProtoMessage() {}

func (x *SearchUserGuidesReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_guide_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUserGuidesReply.ProtoReflect.Descriptor instead.
func (*SearchUserGuidesReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_guide_proto_rawDescGZIP(), []int{11}
}

func (x *SearchUserGuidesReply) GetList() []*UserGuideInfo {
	if x != nil {
		return x.List
	}
	return nil
}

var File_api_wallet_v1_user_guide_proto protoreflect.FileDescriptor

const file_api_wallet_v1_user_guide_proto_rawDesc = "" +
	"\n" +
	"\x1eapi/wallet/v1/user_guide.proto\x12\rapi.wallet.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1bbuf/validate/validate.proto\"\x1c\n" +
	"\x1aListUserGuideCategoriesReq\"X\n" +
	"\x1cListUserGuideCategoriesReply\x128\n" +
	"\x04list\x18\x01 \x03(\v2$.api.wallet.v1.UserGuideCategoryInfoR\x04list\"=\n" +
	"\x11ListUserGuidesReq\x12(\n" +
	"\vcategory_id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\n" +
	"categoryId\"G\n" +
	"\x13ListUserGuidesReply\x120\n" +
	"\x04list\x18\x01 \x03(\v2\x1c.api.wallet.v1.UserGuideInfoR\x04list\"2\n" +
	"\x17ListUserGuideContentReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"T\n" +
	"\x19ListUserGuideContentReply\x127\n" +
	"\x04list\x18\x01 \x03(\v2#.api.wallet.v1.UserGuideContentInfoR\x04list\"r\n" +
	"\x15UserGuideCategoryInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1a\n" +
	"\blanguage\x18\x02 \x01(\tR\blanguage\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x19\n" +
	"\blogo_url\x18\x04 \x01(\tR\alogoUrl\"\x8c\x01\n" +
	"\rUserGuideInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1f\n" +
	"\vcategory_id\x18\x02 \x01(\x04R\n" +
	"categoryId\x12\x1a\n" +
	"\blanguage\x18\x03 \x01(\tR\blanguage\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\x12\x18\n" +
	"\asummary\x18\x05 \x01(\tR\asummary\"\x91\x02\n" +
	"\x12UserGuideBasicInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1f\n" +
	"\vcategory_id\x18\x02 \x01(\x04R\n" +
	"categoryId\x12@\n" +
	"\bcategory\x18\x03 \x01(\v2$.api.wallet.v1.UserGuideCategoryInfoR\bcategory\x12\x1a\n" +
	"\blanguage\x18\x04 \x01(\tR\blanguage\x12\x14\n" +
	"\x05title\x18\x05 \x01(\tR\x05title\x12\x18\n" +
	"\asummary\x18\x06 \x01(\tR\asummary\x12\x1d\n" +
	"\n" +
	"created_at\x18\a \x01(\tR\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\b \x01(\tR\tupdatedAt\"]\n" +
	"\x14UserGuideContentInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x12\x1b\n" +
	"\tphoto_url\x18\x03 \x01(\tR\bphotoUrl\"/\n" +
	"\x13SearchUserGuidesReq\x12\x18\n" +
	"\akeyword\x18\x01 \x01(\tR\akeyword\"I\n" +
	"\x15SearchUserGuidesReply\x120\n" +
	"\x04list\x18\x01 \x03(\v2\x1c.api.wallet.v1.UserGuideInfoR\x04list2\xa6\x04\n" +
	"\fUserGuideSrv\x12\x94\x01\n" +
	"\x17ListUserGuideCategories\x12).api.wallet.v1.ListUserGuideCategoriesReq\x1a+.api.wallet.v1.ListUserGuideCategoriesReply\"!\x82\xd3\xe4\x93\x02\x1b\x12\x19/v1/user-guide-categories\x12o\n" +
	"\x0eListUserGuides\x12 .api.wallet.v1.ListUserGuidesReq\x1a\".api.wallet.v1.ListUserGuidesReply\"\x17\x82\xd3\xe4\x93\x02\x11\x12\x0f/v1/user-guides\x12\x8f\x01\n" +
	"\x14ListUserGuideContent\x12&.api.wallet.v1.ListUserGuideContentReq\x1a(.api.wallet.v1.ListUserGuideContentReply\"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/v1/user-guides/{id}/contents\x12|\n" +
	"\x10SearchUserGuides\x12\".api.wallet.v1.SearchUserGuidesReq\x1a$.api.wallet.v1.SearchUserGuidesReply\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/v1/user-guides/searchB\x96\x01\n" +
	"\x11com.api.wallet.v1B\x0eUserGuideProtoP\x01Z\x1bbyd_wallet/api/wallet/v1;v1\xa2\x02\x03AWX\xaa\x02\rApi.Wallet.V1\xca\x02\rApi\\Wallet\\V1\xe2\x02\x19Api\\Wallet\\V1\\GPBMetadata\xea\x02\x0fApi::Wallet::V1b\x06proto3"

var (
	file_api_wallet_v1_user_guide_proto_rawDescOnce sync.Once
	file_api_wallet_v1_user_guide_proto_rawDescData []byte
)

func file_api_wallet_v1_user_guide_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_user_guide_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_user_guide_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_wallet_v1_user_guide_proto_rawDesc), len(file_api_wallet_v1_user_guide_proto_rawDesc)))
	})
	return file_api_wallet_v1_user_guide_proto_rawDescData
}

var file_api_wallet_v1_user_guide_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_api_wallet_v1_user_guide_proto_goTypes = []any{
	(*ListUserGuideCategoriesReq)(nil),   // 0: api.wallet.v1.ListUserGuideCategoriesReq
	(*ListUserGuideCategoriesReply)(nil), // 1: api.wallet.v1.ListUserGuideCategoriesReply
	(*ListUserGuidesReq)(nil),            // 2: api.wallet.v1.ListUserGuidesReq
	(*ListUserGuidesReply)(nil),          // 3: api.wallet.v1.ListUserGuidesReply
	(*ListUserGuideContentReq)(nil),      // 4: api.wallet.v1.ListUserGuideContentReq
	(*ListUserGuideContentReply)(nil),    // 5: api.wallet.v1.ListUserGuideContentReply
	(*UserGuideCategoryInfo)(nil),        // 6: api.wallet.v1.UserGuideCategoryInfo
	(*UserGuideInfo)(nil),                // 7: api.wallet.v1.UserGuideInfo
	(*UserGuideBasicInfo)(nil),           // 8: api.wallet.v1.UserGuideBasicInfo
	(*UserGuideContentInfo)(nil),         // 9: api.wallet.v1.UserGuideContentInfo
	(*SearchUserGuidesReq)(nil),          // 10: api.wallet.v1.SearchUserGuidesReq
	(*SearchUserGuidesReply)(nil),        // 11: api.wallet.v1.SearchUserGuidesReply
}
var file_api_wallet_v1_user_guide_proto_depIdxs = []int32{
	6,  // 0: api.wallet.v1.ListUserGuideCategoriesReply.list:type_name -> api.wallet.v1.UserGuideCategoryInfo
	7,  // 1: api.wallet.v1.ListUserGuidesReply.list:type_name -> api.wallet.v1.UserGuideInfo
	9,  // 2: api.wallet.v1.ListUserGuideContentReply.list:type_name -> api.wallet.v1.UserGuideContentInfo
	6,  // 3: api.wallet.v1.UserGuideBasicInfo.category:type_name -> api.wallet.v1.UserGuideCategoryInfo
	7,  // 4: api.wallet.v1.SearchUserGuidesReply.list:type_name -> api.wallet.v1.UserGuideInfo
	0,  // 5: api.wallet.v1.UserGuideSrv.ListUserGuideCategories:input_type -> api.wallet.v1.ListUserGuideCategoriesReq
	2,  // 6: api.wallet.v1.UserGuideSrv.ListUserGuides:input_type -> api.wallet.v1.ListUserGuidesReq
	4,  // 7: api.wallet.v1.UserGuideSrv.ListUserGuideContent:input_type -> api.wallet.v1.ListUserGuideContentReq
	10, // 8: api.wallet.v1.UserGuideSrv.SearchUserGuides:input_type -> api.wallet.v1.SearchUserGuidesReq
	1,  // 9: api.wallet.v1.UserGuideSrv.ListUserGuideCategories:output_type -> api.wallet.v1.ListUserGuideCategoriesReply
	3,  // 10: api.wallet.v1.UserGuideSrv.ListUserGuides:output_type -> api.wallet.v1.ListUserGuidesReply
	5,  // 11: api.wallet.v1.UserGuideSrv.ListUserGuideContent:output_type -> api.wallet.v1.ListUserGuideContentReply
	11, // 12: api.wallet.v1.UserGuideSrv.SearchUserGuides:output_type -> api.wallet.v1.SearchUserGuidesReply
	9,  // [9:13] is the sub-list for method output_type
	5,  // [5:9] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_user_guide_proto_init() }
func file_api_wallet_v1_user_guide_proto_init() {
	if File_api_wallet_v1_user_guide_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_wallet_v1_user_guide_proto_rawDesc), len(file_api_wallet_v1_user_guide_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_user_guide_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_user_guide_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_user_guide_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_user_guide_proto = out.File
	file_api_wallet_v1_user_guide_proto_goTypes = nil
	file_api_wallet_v1_user_guide_proto_depIdxs = nil
}
