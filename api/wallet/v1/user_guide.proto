syntax = "proto3";

package api.wallet.v1;

option go_package = "byd_wallet/api/wallet/v1;v1";

import "google/api/annotations.proto";
import "buf/validate/validate.proto";

service UserGuideSrv {
	// 获取用户指南分类列表
	rpc ListUserGuideCategories (ListUserGuideCategoriesReq) returns (ListUserGuideCategoriesReply) {
		option (google.api.http) = {
			get: "/v1/user-guide-categories"
		};
	}
	
	// 根据分类查询用户指南列表
	rpc ListUserGuides (ListUserGuidesReq) returns (ListUserGuidesReply) {
		option (google.api.http) = {
			get: "/v1/user-guides"
		};
	}
	
	// 获取用户指南内容列表
	rpc ListUserGuideContent (ListUserGuideContentReq) returns (ListUserGuideContentReply) {
		option (google.api.http) = {
			get: "/v1/user-guides/{id}/contents"
		};
	}
	
	// 模糊搜索用户指南
	rpc SearchUserGuides (SearchUserGuidesReq) returns (SearchUserGuidesReply) {
		option (google.api.http) = {
			get: "/v1/user-guides/search"
		};
	}
}

message ListUserGuideCategoriesReq {}

message ListUserGuideCategoriesReply {
	// 分类列表
	repeated UserGuideCategoryInfo list = 1;
}

message ListUserGuidesReq {
	// 分类ID
	uint64 category_id = 1 [(buf.validate.field).uint64.gt = 0];
}

message ListUserGuidesReply {
	// 用户指南列表
	repeated UserGuideInfo list = 1;
}

message ListUserGuideContentReq {
	// 用户指南ID
	uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

message ListUserGuideContentReply {
	// 内容列表
	repeated UserGuideContentInfo list = 1;
}

message UserGuideCategoryInfo {
	// 分类ID
	uint64 id = 1;
	// 语言
	string language = 2;
	// 分类名称
	string name = 3;
	// 图标URL
	string logo_url = 4;
}

message UserGuideInfo {
	// 用户指南ID
	uint64 id = 1;
	// 分类ID
	uint64 category_id = 2;
	// 语言
	string language = 3;
	// 标题
	string title = 4;
	// 简介
	string summary = 5;
}

message UserGuideBasicInfo {
	// 用户指南ID
	uint64 id = 1;
	// 分类ID
	uint64 category_id = 2;
	// 分类信息
	UserGuideCategoryInfo category = 3;
	// 语言
	string language = 4;
	// 标题
	string title = 5;
	// 简介
	string summary = 6;
	// 创建时间
	string created_at = 7;
	// 更新时间
	string updated_at = 8;
}

message UserGuideContentInfo {
	// 内容ID
	uint64 id = 1;
	// 正文文本
	string content = 2;
	// 图片URL
	string photo_url = 3;
}

message SearchUserGuidesReq {
	// 搜索关键词
	string keyword = 1;
}

message SearchUserGuidesReply {
	// 用户指南列表
	repeated UserGuideInfo list = 1;
}