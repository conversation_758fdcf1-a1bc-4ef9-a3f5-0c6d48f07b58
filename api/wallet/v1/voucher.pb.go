// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/wallet/v1/voucher.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VoucherRecord struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 券面值
	VoucherValue string `protobuf:"bytes,2,opt,name=voucher_value,json=voucherValue,proto3" json:"voucher_value,omitempty"`
	// 券币种
	VoucherSymbol string `protobuf:"bytes,3,opt,name=voucher_symbol,json=voucherSymbol,proto3" json:"voucher_symbol,omitempty"`
	// 目标值
	Value string `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
	// 目标币种
	Symbol string `protobuf:"bytes,5,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,6,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 目标用户地址
	Address string `protobuf:"bytes,7,opt,name=address,proto3" json:"address,omitempty"`
	// 状态
	Status string `protobuf:"bytes,8,opt,name=status,proto3" json:"status,omitempty"`
	// 结束时间(时间戳秒)
	Timestamp int64 `protobuf:"varint,9,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// token精度
	Decimals      int64 `protobuf:"varint,10,opt,name=decimals,proto3" json:"decimals,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VoucherRecord) Reset() {
	*x = VoucherRecord{}
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VoucherRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoucherRecord) ProtoMessage() {}

func (x *VoucherRecord) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoucherRecord.ProtoReflect.Descriptor instead.
func (*VoucherRecord) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_voucher_proto_rawDescGZIP(), []int{0}
}

func (x *VoucherRecord) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VoucherRecord) GetVoucherValue() string {
	if x != nil {
		return x.VoucherValue
	}
	return ""
}

func (x *VoucherRecord) GetVoucherSymbol() string {
	if x != nil {
		return x.VoucherSymbol
	}
	return ""
}

func (x *VoucherRecord) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *VoucherRecord) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *VoucherRecord) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *VoucherRecord) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *VoucherRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *VoucherRecord) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *VoucherRecord) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

type Voucher struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 核销金额(token最小单位)
	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	// 状态：1 未核销 2 已核销
	Status        int64  `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	Token         *Token `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Voucher) Reset() {
	*x = Voucher{}
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Voucher) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Voucher) ProtoMessage() {}

func (x *Voucher) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Voucher.ProtoReflect.Descriptor instead.
func (*Voucher) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_voucher_proto_rawDescGZIP(), []int{1}
}

func (x *Voucher) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Voucher) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Voucher) GetToken() *Token {
	if x != nil {
		return x.Token
	}
	return nil
}

type GetVoucherReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 券码
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	// 昵称
	Nickname      string `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVoucherReq) Reset() {
	*x = GetVoucherReq{}
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVoucherReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVoucherReq) ProtoMessage() {}

func (x *GetVoucherReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVoucherReq.ProtoReflect.Descriptor instead.
func (*GetVoucherReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_voucher_proto_rawDescGZIP(), []int{2}
}

func (x *GetVoucherReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *GetVoucherReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

type GetVoucherReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Voucher               `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVoucherReply) Reset() {
	*x = GetVoucherReply{}
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVoucherReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVoucherReply) ProtoMessage() {}

func (x *GetVoucherReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVoucherReply.ProtoReflect.Descriptor instead.
func (*GetVoucherReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_voucher_proto_rawDescGZIP(), []int{3}
}

func (x *GetVoucherReply) GetData() *Voucher {
	if x != nil {
		return x.Data
	}
	return nil
}

type ListVoucherTokenReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListVoucherTokenReq) Reset() {
	*x = ListVoucherTokenReq{}
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListVoucherTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVoucherTokenReq) ProtoMessage() {}

func (x *ListVoucherTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVoucherTokenReq.ProtoReflect.Descriptor instead.
func (*ListVoucherTokenReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_voucher_proto_rawDescGZIP(), []int{4}
}

type ListVoucherTokenReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*Token               `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListVoucherTokenReply) Reset() {
	*x = ListVoucherTokenReply{}
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListVoucherTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVoucherTokenReply) ProtoMessage() {}

func (x *ListVoucherTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVoucherTokenReply.ProtoReflect.Descriptor instead.
func (*ListVoucherTokenReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_voucher_proto_rawDescGZIP(), []int{5}
}

func (x *ListVoucherTokenReply) GetList() []*Token {
	if x != nil {
		return x.List
	}
	return nil
}

type RedeemReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 昵称
	Nickname string `protobuf:"bytes,1,opt,name=nickname,proto3" json:"nickname,omitempty"`
	// 兑换券编号
	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,3,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 用户地址
	Address       string `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RedeemReq) Reset() {
	*x = RedeemReq{}
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RedeemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedeemReq) ProtoMessage() {}

func (x *RedeemReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedeemReq.ProtoReflect.Descriptor instead.
func (*RedeemReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_voucher_proto_rawDescGZIP(), []int{6}
}

func (x *RedeemReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *RedeemReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *RedeemReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *RedeemReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type RedeemReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RedeemReply) Reset() {
	*x = RedeemReply{}
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RedeemReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedeemReply) ProtoMessage() {}

func (x *RedeemReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedeemReply.ProtoReflect.Descriptor instead.
func (*RedeemReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_voucher_proto_rawDescGZIP(), []int{7}
}

type ListVoucherRecordReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 页码 从1开始
	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页展示条数
	Limit int64 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	// 用户地址
	Addresses     []string `protobuf:"bytes,3,rep,name=addresses,proto3" json:"addresses,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListVoucherRecordReq) Reset() {
	*x = ListVoucherRecordReq{}
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListVoucherRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVoucherRecordReq) ProtoMessage() {}

func (x *ListVoucherRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVoucherRecordReq.ProtoReflect.Descriptor instead.
func (*ListVoucherRecordReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_voucher_proto_rawDescGZIP(), []int{8}
}

func (x *ListVoucherRecordReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListVoucherRecordReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListVoucherRecordReq) GetAddresses() []string {
	if x != nil {
		return x.Addresses
	}
	return nil
}

type ListVoucherRecordReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 兑换记录
	List []*VoucherRecord `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总条数
	Count         int64 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListVoucherRecordReply) Reset() {
	*x = ListVoucherRecordReply{}
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListVoucherRecordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVoucherRecordReply) ProtoMessage() {}

func (x *ListVoucherRecordReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_voucher_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVoucherRecordReply.ProtoReflect.Descriptor instead.
func (*ListVoucherRecordReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_voucher_proto_rawDescGZIP(), []int{9}
}

func (x *ListVoucherRecordReply) GetList() []*VoucherRecord {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListVoucherRecordReply) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

var File_api_wallet_v1_voucher_proto protoreflect.FileDescriptor

const file_api_wallet_v1_voucher_proto_rawDesc = "" +
	"\n" +
	"\x1bapi/wallet/v1/voucher.proto\x12\rapi.wallet.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1bbuf/validate/validate.proto\x1a\x1aapi/wallet/v1/wallet.proto\"\xa6\x02\n" +
	"\rVoucherRecord\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12#\n" +
	"\rvoucher_value\x18\x02 \x01(\tR\fvoucherValue\x12%\n" +
	"\x0evoucher_symbol\x18\x03 \x01(\tR\rvoucherSymbol\x12\x14\n" +
	"\x05value\x18\x04 \x01(\tR\x05value\x12\x16\n" +
	"\x06symbol\x18\x05 \x01(\tR\x06symbol\x12\x1f\n" +
	"\vchain_index\x18\x06 \x01(\x03R\n" +
	"chainIndex\x12\x18\n" +
	"\aaddress\x18\a \x01(\tR\aaddress\x12\x16\n" +
	"\x06status\x18\b \x01(\tR\x06status\x12\x1c\n" +
	"\ttimestamp\x18\t \x01(\x03R\ttimestamp\x12\x1a\n" +
	"\bdecimals\x18\n" +
	" \x01(\x03R\bdecimals\"c\n" +
	"\aVoucher\x12\x14\n" +
	"\x05value\x18\x01 \x01(\tR\x05value\x12\x16\n" +
	"\x06status\x18\x02 \x01(\x03R\x06status\x12*\n" +
	"\x05token\x18\x03 \x01(\v2\x14.api.wallet.v1.TokenR\x05token\"?\n" +
	"\rGetVoucherReq\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x1a\n" +
	"\bnickname\x18\x02 \x01(\tR\bnickname\"=\n" +
	"\x0fGetVoucherReply\x12*\n" +
	"\x04data\x18\x01 \x01(\v2\x16.api.wallet.v1.VoucherR\x04data\"\x15\n" +
	"\x13ListVoucherTokenReq\"A\n" +
	"\x15ListVoucherTokenReply\x12(\n" +
	"\x04list\x18\x01 \x03(\v2\x14.api.wallet.v1.TokenR\x04list\"\x9a\x01\n" +
	"\tRedeemReq\x12#\n" +
	"\bnickname\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\bnickname\x12\x1b\n" +
	"\x04code\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x04code\x12(\n" +
	"\vchain_index\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02(\x00R\n" +
	"chainIndex\x12!\n" +
	"\aaddress\x18\x04 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\aaddress\"\r\n" +
	"\vRedeemReply\"r\n" +
	"\x14ListVoucherRecordReq\x12\x1b\n" +
	"\x04page\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x04page\x12\x1f\n" +
	"\x05limit\x18\x02 \x01(\x03B\t\xbaH\x06\"\x04\x18d \x00R\x05limit\x12\x1c\n" +
	"\taddresses\x18\x03 \x03(\tR\taddresses\"`\n" +
	"\x16ListVoucherRecordReply\x120\n" +
	"\x04list\x18\x01 \x03(\v2\x1c.api.wallet.v1.VoucherRecordR\x04list\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x03R\x05count2\xbe\x03\n" +
	"\n" +
	"VoucherSrv\x12]\n" +
	"\x06Redeem\x12\x18.api.wallet.v1.RedeemReq\x1a\x1a.api.wallet.v1.RedeemReply\"\x1d\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/v1/voucher/redeem\x12~\n" +
	"\x11ListVoucherRecord\x12#.api.wallet.v1.ListVoucherRecordReq\x1a%.api.wallet.v1.ListVoucherRecordReply\"\x1d\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/v1/voucher/record\x12p\n" +
	"\tListToken\x12\".api.wallet.v1.ListVoucherTokenReq\x1a$.api.wallet.v1.ListVoucherTokenReply\"\x19\x82\xd3\xe4\x93\x02\x13\x12\x11/v1/voucher/token\x12_\n" +
	"\n" +
	"GetVoucher\x12\x1c.api.wallet.v1.GetVoucherReq\x1a\x1e.api.wallet.v1.GetVoucherReply\"\x13\x82\xd3\xe4\x93\x02\r\x12\v/v1/voucherB\x94\x01\n" +
	"\x11com.api.wallet.v1B\fVoucherProtoP\x01Z\x1bbyd_wallet/api/wallet/v1;v1\xa2\x02\x03AWX\xaa\x02\rApi.Wallet.V1\xca\x02\rApi\\Wallet\\V1\xe2\x02\x19Api\\Wallet\\V1\\GPBMetadata\xea\x02\x0fApi::Wallet::V1b\x06proto3"

var (
	file_api_wallet_v1_voucher_proto_rawDescOnce sync.Once
	file_api_wallet_v1_voucher_proto_rawDescData []byte
)

func file_api_wallet_v1_voucher_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_voucher_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_voucher_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_wallet_v1_voucher_proto_rawDesc), len(file_api_wallet_v1_voucher_proto_rawDesc)))
	})
	return file_api_wallet_v1_voucher_proto_rawDescData
}

var file_api_wallet_v1_voucher_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_wallet_v1_voucher_proto_goTypes = []any{
	(*VoucherRecord)(nil),          // 0: api.wallet.v1.VoucherRecord
	(*Voucher)(nil),                // 1: api.wallet.v1.Voucher
	(*GetVoucherReq)(nil),          // 2: api.wallet.v1.GetVoucherReq
	(*GetVoucherReply)(nil),        // 3: api.wallet.v1.GetVoucherReply
	(*ListVoucherTokenReq)(nil),    // 4: api.wallet.v1.ListVoucherTokenReq
	(*ListVoucherTokenReply)(nil),  // 5: api.wallet.v1.ListVoucherTokenReply
	(*RedeemReq)(nil),              // 6: api.wallet.v1.RedeemReq
	(*RedeemReply)(nil),            // 7: api.wallet.v1.RedeemReply
	(*ListVoucherRecordReq)(nil),   // 8: api.wallet.v1.ListVoucherRecordReq
	(*ListVoucherRecordReply)(nil), // 9: api.wallet.v1.ListVoucherRecordReply
	(*Token)(nil),                  // 10: api.wallet.v1.Token
}
var file_api_wallet_v1_voucher_proto_depIdxs = []int32{
	10, // 0: api.wallet.v1.Voucher.token:type_name -> api.wallet.v1.Token
	1,  // 1: api.wallet.v1.GetVoucherReply.data:type_name -> api.wallet.v1.Voucher
	10, // 2: api.wallet.v1.ListVoucherTokenReply.list:type_name -> api.wallet.v1.Token
	0,  // 3: api.wallet.v1.ListVoucherRecordReply.list:type_name -> api.wallet.v1.VoucherRecord
	6,  // 4: api.wallet.v1.VoucherSrv.Redeem:input_type -> api.wallet.v1.RedeemReq
	8,  // 5: api.wallet.v1.VoucherSrv.ListVoucherRecord:input_type -> api.wallet.v1.ListVoucherRecordReq
	4,  // 6: api.wallet.v1.VoucherSrv.ListToken:input_type -> api.wallet.v1.ListVoucherTokenReq
	2,  // 7: api.wallet.v1.VoucherSrv.GetVoucher:input_type -> api.wallet.v1.GetVoucherReq
	7,  // 8: api.wallet.v1.VoucherSrv.Redeem:output_type -> api.wallet.v1.RedeemReply
	9,  // 9: api.wallet.v1.VoucherSrv.ListVoucherRecord:output_type -> api.wallet.v1.ListVoucherRecordReply
	5,  // 10: api.wallet.v1.VoucherSrv.ListToken:output_type -> api.wallet.v1.ListVoucherTokenReply
	3,  // 11: api.wallet.v1.VoucherSrv.GetVoucher:output_type -> api.wallet.v1.GetVoucherReply
	8,  // [8:12] is the sub-list for method output_type
	4,  // [4:8] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_voucher_proto_init() }
func file_api_wallet_v1_voucher_proto_init() {
	if File_api_wallet_v1_voucher_proto != nil {
		return
	}
	file_api_wallet_v1_wallet_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_wallet_v1_voucher_proto_rawDesc), len(file_api_wallet_v1_voucher_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_voucher_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_voucher_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_voucher_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_voucher_proto = out.File
	file_api_wallet_v1_voucher_proto_goTypes = nil
	file_api_wallet_v1_voucher_proto_depIdxs = nil
}
