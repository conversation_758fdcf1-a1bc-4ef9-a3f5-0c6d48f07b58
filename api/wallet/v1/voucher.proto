syntax = "proto3";

package api.wallet.v1;

option go_package = "byd_wallet/api/wallet/v1;v1";

import "google/api/annotations.proto";
import "buf/validate/validate.proto";
import "api/wallet/v1/wallet.proto";

service VoucherSrv {
  // 兑换券兑换
  rpc Redeem (RedeemReq) returns (RedeemReply) {
    option (google.api.http) = {
      post: "/v1/voucher/redeem"
      body: "*"
    };
  }
  // 兑换记录列表
  rpc ListVoucherRecord (ListVoucherRecordReq) returns (ListVoucherRecordReply) {
    option (google.api.http) = {
      post: "/v1/voucher/record"
      body: "*"
    };
  }
  // 查询兑换券的币种列表
  rpc ListToken (ListVoucherTokenReq) returns (ListVoucherTokenReply) {
    option (google.api.http) = {
      get: "/v1/voucher/token"
    };
  }
  // 查询兑换券信息
  rpc GetVoucher (GetVoucherReq) returns (GetVoucherReply) {
    option (google.api.http) = {
      get: "/v1/voucher"
    };
  }
}

message VoucherRecord {
  // 记录id
  uint64 id = 1;
  // 券面值
  string voucher_value = 2;
  // 券币种
  string voucher_symbol = 3;
  // 目标值
  string value = 4;
  // 目标币种
  string symbol = 5;
  // 链索引
  int64 chain_index = 6;
  // 目标用户地址
  string address = 7;
  // 状态
  string status = 8;
  // 结束时间(时间戳秒)
  int64 timestamp = 9;
  // token精度
  int64 decimals = 10;
}

message Voucher {
  // 核销金额(token最小单位)
  string value = 1;
  // 状态：1 未核销 2 已核销
  int64 status = 2;
  Token token = 3;
}

message GetVoucherReq {
  // 券码
  string code = 1;
  // 昵称
  string nickname = 2;
}

message GetVoucherReply {
  Voucher data = 1;
}

message ListVoucherTokenReq {}

message ListVoucherTokenReply {
  repeated Token list = 1;
}

message RedeemReq {
  // 昵称
  string nickname = 1 [(buf.validate.field).string.min_len = 1];
  // 兑换券编号
  string code = 2 [(buf.validate.field).string.min_len = 1];
  // 链索引
  int64 chain_index = 3 [(buf.validate.field).int64.gte = 0];
  // 用户地址
  string address = 4 [(buf.validate.field).string.min_len = 1];
}

message RedeemReply {}

message ListVoucherRecordReq {
  // 页码 从1开始
  int64 page = 1 [(buf.validate.field).int64.gt = 0];
  // 每页展示条数
  int64 limit = 2 [(buf.validate.field).int64 = {
    gt: 0
    lte: 100
  }];
  // 用户地址
  repeated string addresses = 3;
}

message ListVoucherRecordReply {
  // 兑换记录
  repeated VoucherRecord list = 1;
  // 总条数
  int64 count = 2;
}
