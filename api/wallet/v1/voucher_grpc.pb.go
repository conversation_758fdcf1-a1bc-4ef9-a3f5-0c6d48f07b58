// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/wallet/v1/voucher.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	VoucherSrv_Redeem_FullMethodName            = "/api.wallet.v1.VoucherSrv/Redeem"
	VoucherSrv_ListVoucherRecord_FullMethodName = "/api.wallet.v1.VoucherSrv/ListVoucherRecord"
	VoucherSrv_ListToken_FullMethodName         = "/api.wallet.v1.VoucherSrv/ListToken"
	VoucherSrv_GetVoucher_FullMethodName        = "/api.wallet.v1.VoucherSrv/GetVoucher"
)

// VoucherSrvClient is the client API for VoucherSrv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VoucherSrvClient interface {
	// 兑换券兑换
	Redeem(ctx context.Context, in *RedeemReq, opts ...grpc.CallOption) (*RedeemReply, error)
	// 兑换记录列表
	ListVoucherRecord(ctx context.Context, in *ListVoucherRecordReq, opts ...grpc.CallOption) (*ListVoucherRecordReply, error)
	// 查询兑换券的币种列表
	ListToken(ctx context.Context, in *ListVoucherTokenReq, opts ...grpc.CallOption) (*ListVoucherTokenReply, error)
	// 查询兑换券信息
	GetVoucher(ctx context.Context, in *GetVoucherReq, opts ...grpc.CallOption) (*GetVoucherReply, error)
}

type voucherSrvClient struct {
	cc grpc.ClientConnInterface
}

func NewVoucherSrvClient(cc grpc.ClientConnInterface) VoucherSrvClient {
	return &voucherSrvClient{cc}
}

func (c *voucherSrvClient) Redeem(ctx context.Context, in *RedeemReq, opts ...grpc.CallOption) (*RedeemReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RedeemReply)
	err := c.cc.Invoke(ctx, VoucherSrv_Redeem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voucherSrvClient) ListVoucherRecord(ctx context.Context, in *ListVoucherRecordReq, opts ...grpc.CallOption) (*ListVoucherRecordReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListVoucherRecordReply)
	err := c.cc.Invoke(ctx, VoucherSrv_ListVoucherRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voucherSrvClient) ListToken(ctx context.Context, in *ListVoucherTokenReq, opts ...grpc.CallOption) (*ListVoucherTokenReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListVoucherTokenReply)
	err := c.cc.Invoke(ctx, VoucherSrv_ListToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voucherSrvClient) GetVoucher(ctx context.Context, in *GetVoucherReq, opts ...grpc.CallOption) (*GetVoucherReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetVoucherReply)
	err := c.cc.Invoke(ctx, VoucherSrv_GetVoucher_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VoucherSrvServer is the server API for VoucherSrv service.
// All implementations must embed UnimplementedVoucherSrvServer
// for forward compatibility.
type VoucherSrvServer interface {
	// 兑换券兑换
	Redeem(context.Context, *RedeemReq) (*RedeemReply, error)
	// 兑换记录列表
	ListVoucherRecord(context.Context, *ListVoucherRecordReq) (*ListVoucherRecordReply, error)
	// 查询兑换券的币种列表
	ListToken(context.Context, *ListVoucherTokenReq) (*ListVoucherTokenReply, error)
	// 查询兑换券信息
	GetVoucher(context.Context, *GetVoucherReq) (*GetVoucherReply, error)
	mustEmbedUnimplementedVoucherSrvServer()
}

// UnimplementedVoucherSrvServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedVoucherSrvServer struct{}

func (UnimplementedVoucherSrvServer) Redeem(context.Context, *RedeemReq) (*RedeemReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Redeem not implemented")
}
func (UnimplementedVoucherSrvServer) ListVoucherRecord(context.Context, *ListVoucherRecordReq) (*ListVoucherRecordReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListVoucherRecord not implemented")
}
func (UnimplementedVoucherSrvServer) ListToken(context.Context, *ListVoucherTokenReq) (*ListVoucherTokenReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListToken not implemented")
}
func (UnimplementedVoucherSrvServer) GetVoucher(context.Context, *GetVoucherReq) (*GetVoucherReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVoucher not implemented")
}
func (UnimplementedVoucherSrvServer) mustEmbedUnimplementedVoucherSrvServer() {}
func (UnimplementedVoucherSrvServer) testEmbeddedByValue()                    {}

// UnsafeVoucherSrvServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VoucherSrvServer will
// result in compilation errors.
type UnsafeVoucherSrvServer interface {
	mustEmbedUnimplementedVoucherSrvServer()
}

func RegisterVoucherSrvServer(s grpc.ServiceRegistrar, srv VoucherSrvServer) {
	// If the following call pancis, it indicates UnimplementedVoucherSrvServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&VoucherSrv_ServiceDesc, srv)
}

func _VoucherSrv_Redeem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RedeemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherSrvServer).Redeem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VoucherSrv_Redeem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherSrvServer).Redeem(ctx, req.(*RedeemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VoucherSrv_ListVoucherRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListVoucherRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherSrvServer).ListVoucherRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VoucherSrv_ListVoucherRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherSrvServer).ListVoucherRecord(ctx, req.(*ListVoucherRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VoucherSrv_ListToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListVoucherTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherSrvServer).ListToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VoucherSrv_ListToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherSrvServer).ListToken(ctx, req.(*ListVoucherTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VoucherSrv_GetVoucher_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVoucherReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherSrvServer).GetVoucher(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VoucherSrv_GetVoucher_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherSrvServer).GetVoucher(ctx, req.(*GetVoucherReq))
	}
	return interceptor(ctx, in, info, handler)
}

// VoucherSrv_ServiceDesc is the grpc.ServiceDesc for VoucherSrv service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var VoucherSrv_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.wallet.v1.VoucherSrv",
	HandlerType: (*VoucherSrvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Redeem",
			Handler:    _VoucherSrv_Redeem_Handler,
		},
		{
			MethodName: "ListVoucherRecord",
			Handler:    _VoucherSrv_ListVoucherRecord_Handler,
		},
		{
			MethodName: "ListToken",
			Handler:    _VoucherSrv_ListToken_Handler,
		},
		{
			MethodName: "GetVoucher",
			Handler:    _VoucherSrv_GetVoucher_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/wallet/v1/voucher.proto",
}
