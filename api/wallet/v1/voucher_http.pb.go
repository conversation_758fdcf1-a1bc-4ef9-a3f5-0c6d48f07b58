// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: api/wallet/v1/voucher.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationVoucherSrvGetVoucher = "/api.wallet.v1.VoucherSrv/GetVoucher"
const OperationVoucherSrvListToken = "/api.wallet.v1.VoucherSrv/ListToken"
const OperationVoucherSrvListVoucherRecord = "/api.wallet.v1.VoucherSrv/ListVoucherRecord"
const OperationVoucherSrvRedeem = "/api.wallet.v1.VoucherSrv/Redeem"

type VoucherSrvHTTPServer interface {
	// GetVoucher 查询兑换券信息
	GetVoucher(context.Context, *GetVoucherReq) (*GetVoucherReply, error)
	// ListToken 查询兑换券的币种列表
	ListToken(context.Context, *ListVoucherTokenReq) (*ListVoucherTokenReply, error)
	// ListVoucherRecord 兑换记录列表
	ListVoucherRecord(context.Context, *ListVoucherRecordReq) (*ListVoucherRecordReply, error)
	// Redeem 兑换券兑换
	Redeem(context.Context, *RedeemReq) (*RedeemReply, error)
}

func RegisterVoucherSrvHTTPServer(s *http.Server, srv VoucherSrvHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/voucher/redeem", _VoucherSrv_Redeem0_HTTP_Handler(srv))
	r.POST("/v1/voucher/record", _VoucherSrv_ListVoucherRecord0_HTTP_Handler(srv))
	r.GET("/v1/voucher/token", _VoucherSrv_ListToken1_HTTP_Handler(srv))
	r.GET("/v1/voucher", _VoucherSrv_GetVoucher0_HTTP_Handler(srv))
}

func _VoucherSrv_Redeem0_HTTP_Handler(srv VoucherSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RedeemReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVoucherSrvRedeem)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Redeem(ctx, req.(*RedeemReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RedeemReply)
		return ctx.Result(200, reply)
	}
}

func _VoucherSrv_ListVoucherRecord0_HTTP_Handler(srv VoucherSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListVoucherRecordReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVoucherSrvListVoucherRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListVoucherRecord(ctx, req.(*ListVoucherRecordReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListVoucherRecordReply)
		return ctx.Result(200, reply)
	}
}

func _VoucherSrv_ListToken1_HTTP_Handler(srv VoucherSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListVoucherTokenReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVoucherSrvListToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListToken(ctx, req.(*ListVoucherTokenReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListVoucherTokenReply)
		return ctx.Result(200, reply)
	}
}

func _VoucherSrv_GetVoucher0_HTTP_Handler(srv VoucherSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetVoucherReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVoucherSrvGetVoucher)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetVoucher(ctx, req.(*GetVoucherReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetVoucherReply)
		return ctx.Result(200, reply)
	}
}

type VoucherSrvHTTPClient interface {
	GetVoucher(ctx context.Context, req *GetVoucherReq, opts ...http.CallOption) (rsp *GetVoucherReply, err error)
	ListToken(ctx context.Context, req *ListVoucherTokenReq, opts ...http.CallOption) (rsp *ListVoucherTokenReply, err error)
	ListVoucherRecord(ctx context.Context, req *ListVoucherRecordReq, opts ...http.CallOption) (rsp *ListVoucherRecordReply, err error)
	Redeem(ctx context.Context, req *RedeemReq, opts ...http.CallOption) (rsp *RedeemReply, err error)
}

type VoucherSrvHTTPClientImpl struct {
	cc *http.Client
}

func NewVoucherSrvHTTPClient(client *http.Client) VoucherSrvHTTPClient {
	return &VoucherSrvHTTPClientImpl{client}
}

func (c *VoucherSrvHTTPClientImpl) GetVoucher(ctx context.Context, in *GetVoucherReq, opts ...http.CallOption) (*GetVoucherReply, error) {
	var out GetVoucherReply
	pattern := "/v1/voucher"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationVoucherSrvGetVoucher))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *VoucherSrvHTTPClientImpl) ListToken(ctx context.Context, in *ListVoucherTokenReq, opts ...http.CallOption) (*ListVoucherTokenReply, error) {
	var out ListVoucherTokenReply
	pattern := "/v1/voucher/token"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationVoucherSrvListToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *VoucherSrvHTTPClientImpl) ListVoucherRecord(ctx context.Context, in *ListVoucherRecordReq, opts ...http.CallOption) (*ListVoucherRecordReply, error) {
	var out ListVoucherRecordReply
	pattern := "/v1/voucher/record"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationVoucherSrvListVoucherRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *VoucherSrvHTTPClientImpl) Redeem(ctx context.Context, in *RedeemReq, opts ...http.CallOption) (*RedeemReply, error) {
	var out RedeemReply
	pattern := "/v1/voucher/redeem"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationVoucherSrvRedeem))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
