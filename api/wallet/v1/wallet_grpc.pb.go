// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/wallet/v1/wallet.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	WalletSrv_NetworkList_FullMethodName           = "/api.wallet.v1.WalletSrv/NetworkList"
	WalletSrv_Transactions_FullMethodName          = "/api.wallet.v1.WalletSrv/Transactions"
	WalletSrv_TransactionsByAddress_FullMethodName = "/api.wallet.v1.WalletSrv/TransactionsByAddress"
	WalletSrv_TransactionInfo_FullMethodName       = "/api.wallet.v1.WalletSrv/TransactionInfo"
	WalletSrv_TokenList_FullMethodName             = "/api.wallet.v1.WalletSrv/TokenList"
	WalletSrv_GetToken_FullMethodName              = "/api.wallet.v1.WalletSrv/GetToken"
	WalletSrv_ListTokenBalance_FullMethodName      = "/api.wallet.v1.WalletSrv/ListTokenBalance"
	WalletSrv_QueryTxLatestToken_FullMethodName    = "/api.wallet.v1.WalletSrv/QueryTxLatestToken"
	WalletSrv_ReportInternalTxn_FullMethodName     = "/api.wallet.v1.WalletSrv/ReportInternalTxn"
)

// WalletSrvClient is the client API for WalletSrv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WalletSrvClient interface {
	// 链列表
	NetworkList(ctx context.Context, in *NetworkListReq, opts ...grpc.CallOption) (*NetworkListReply, error)
	// 获取交易信息
	Transactions(ctx context.Context, in *TransactionsReq, opts ...grpc.CallOption) (*TransactionsReply, error)
	// 获取交易信息
	TransactionsByAddress(ctx context.Context, in *TransactionsByAddressReq, opts ...grpc.CallOption) (*TransactionsReply, error)
	// 获取交易详情信息
	TransactionInfo(ctx context.Context, in *TransactionReq, opts ...grpc.CallOption) (*TransactionDetail, error)
	// 获取查询token
	TokenList(ctx context.Context, in *TokenListReq, opts ...grpc.CallOption) (*TokenListReply, error)
	// 获取指定token
	GetToken(ctx context.Context, in *GetTokenReq, opts ...grpc.CallOption) (*Token, error)
	// 获取地址的资产明细
	ListTokenBalance(ctx context.Context, in *ListTokenBalanceReq, opts ...grpc.CallOption) (*ListTokenBalanceReply, error)
	// 获取地址接受转账的最新币种信息
	QueryTxLatestToken(ctx context.Context, in *QueryTxLatestTokenReq, opts ...grpc.CallOption) (*QueryTxLatestTokenReply, error)
	// 上报并更新一笔内部交易
	ReportInternalTxn(ctx context.Context, in *ReportInternalTxnReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type walletSrvClient struct {
	cc grpc.ClientConnInterface
}

func NewWalletSrvClient(cc grpc.ClientConnInterface) WalletSrvClient {
	return &walletSrvClient{cc}
}

func (c *walletSrvClient) NetworkList(ctx context.Context, in *NetworkListReq, opts ...grpc.CallOption) (*NetworkListReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NetworkListReply)
	err := c.cc.Invoke(ctx, WalletSrv_NetworkList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletSrvClient) Transactions(ctx context.Context, in *TransactionsReq, opts ...grpc.CallOption) (*TransactionsReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TransactionsReply)
	err := c.cc.Invoke(ctx, WalletSrv_Transactions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletSrvClient) TransactionsByAddress(ctx context.Context, in *TransactionsByAddressReq, opts ...grpc.CallOption) (*TransactionsReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TransactionsReply)
	err := c.cc.Invoke(ctx, WalletSrv_TransactionsByAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletSrvClient) TransactionInfo(ctx context.Context, in *TransactionReq, opts ...grpc.CallOption) (*TransactionDetail, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TransactionDetail)
	err := c.cc.Invoke(ctx, WalletSrv_TransactionInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletSrvClient) TokenList(ctx context.Context, in *TokenListReq, opts ...grpc.CallOption) (*TokenListReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TokenListReply)
	err := c.cc.Invoke(ctx, WalletSrv_TokenList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletSrvClient) GetToken(ctx context.Context, in *GetTokenReq, opts ...grpc.CallOption) (*Token, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Token)
	err := c.cc.Invoke(ctx, WalletSrv_GetToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletSrvClient) ListTokenBalance(ctx context.Context, in *ListTokenBalanceReq, opts ...grpc.CallOption) (*ListTokenBalanceReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTokenBalanceReply)
	err := c.cc.Invoke(ctx, WalletSrv_ListTokenBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletSrvClient) QueryTxLatestToken(ctx context.Context, in *QueryTxLatestTokenReq, opts ...grpc.CallOption) (*QueryTxLatestTokenReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryTxLatestTokenReply)
	err := c.cc.Invoke(ctx, WalletSrv_QueryTxLatestToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletSrvClient) ReportInternalTxn(ctx context.Context, in *ReportInternalTxnReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, WalletSrv_ReportInternalTxn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WalletSrvServer is the server API for WalletSrv service.
// All implementations must embed UnimplementedWalletSrvServer
// for forward compatibility.
type WalletSrvServer interface {
	// 链列表
	NetworkList(context.Context, *NetworkListReq) (*NetworkListReply, error)
	// 获取交易信息
	Transactions(context.Context, *TransactionsReq) (*TransactionsReply, error)
	// 获取交易信息
	TransactionsByAddress(context.Context, *TransactionsByAddressReq) (*TransactionsReply, error)
	// 获取交易详情信息
	TransactionInfo(context.Context, *TransactionReq) (*TransactionDetail, error)
	// 获取查询token
	TokenList(context.Context, *TokenListReq) (*TokenListReply, error)
	// 获取指定token
	GetToken(context.Context, *GetTokenReq) (*Token, error)
	// 获取地址的资产明细
	ListTokenBalance(context.Context, *ListTokenBalanceReq) (*ListTokenBalanceReply, error)
	// 获取地址接受转账的最新币种信息
	QueryTxLatestToken(context.Context, *QueryTxLatestTokenReq) (*QueryTxLatestTokenReply, error)
	// 上报并更新一笔内部交易
	ReportInternalTxn(context.Context, *ReportInternalTxnReq) (*emptypb.Empty, error)
	mustEmbedUnimplementedWalletSrvServer()
}

// UnimplementedWalletSrvServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedWalletSrvServer struct{}

func (UnimplementedWalletSrvServer) NetworkList(context.Context, *NetworkListReq) (*NetworkListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NetworkList not implemented")
}
func (UnimplementedWalletSrvServer) Transactions(context.Context, *TransactionsReq) (*TransactionsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Transactions not implemented")
}
func (UnimplementedWalletSrvServer) TransactionsByAddress(context.Context, *TransactionsByAddressReq) (*TransactionsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TransactionsByAddress not implemented")
}
func (UnimplementedWalletSrvServer) TransactionInfo(context.Context, *TransactionReq) (*TransactionDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TransactionInfo not implemented")
}
func (UnimplementedWalletSrvServer) TokenList(context.Context, *TokenListReq) (*TokenListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TokenList not implemented")
}
func (UnimplementedWalletSrvServer) GetToken(context.Context, *GetTokenReq) (*Token, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetToken not implemented")
}
func (UnimplementedWalletSrvServer) ListTokenBalance(context.Context, *ListTokenBalanceReq) (*ListTokenBalanceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTokenBalance not implemented")
}
func (UnimplementedWalletSrvServer) QueryTxLatestToken(context.Context, *QueryTxLatestTokenReq) (*QueryTxLatestTokenReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryTxLatestToken not implemented")
}
func (UnimplementedWalletSrvServer) ReportInternalTxn(context.Context, *ReportInternalTxnReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportInternalTxn not implemented")
}
func (UnimplementedWalletSrvServer) mustEmbedUnimplementedWalletSrvServer() {}
func (UnimplementedWalletSrvServer) testEmbeddedByValue()                   {}

// UnsafeWalletSrvServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WalletSrvServer will
// result in compilation errors.
type UnsafeWalletSrvServer interface {
	mustEmbedUnimplementedWalletSrvServer()
}

func RegisterWalletSrvServer(s grpc.ServiceRegistrar, srv WalletSrvServer) {
	// If the following call pancis, it indicates UnimplementedWalletSrvServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&WalletSrv_ServiceDesc, srv)
}

func _WalletSrv_NetworkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NetworkListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletSrvServer).NetworkList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletSrv_NetworkList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletSrvServer).NetworkList(ctx, req.(*NetworkListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletSrv_Transactions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransactionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletSrvServer).Transactions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletSrv_Transactions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletSrvServer).Transactions(ctx, req.(*TransactionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletSrv_TransactionsByAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransactionsByAddressReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletSrvServer).TransactionsByAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletSrv_TransactionsByAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletSrvServer).TransactionsByAddress(ctx, req.(*TransactionsByAddressReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletSrv_TransactionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransactionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletSrvServer).TransactionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletSrv_TransactionInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletSrvServer).TransactionInfo(ctx, req.(*TransactionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletSrv_TokenList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TokenListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletSrvServer).TokenList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletSrv_TokenList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletSrvServer).TokenList(ctx, req.(*TokenListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletSrv_GetToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletSrvServer).GetToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletSrv_GetToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletSrvServer).GetToken(ctx, req.(*GetTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletSrv_ListTokenBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTokenBalanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletSrvServer).ListTokenBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletSrv_ListTokenBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletSrvServer).ListTokenBalance(ctx, req.(*ListTokenBalanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletSrv_QueryTxLatestToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryTxLatestTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletSrvServer).QueryTxLatestToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletSrv_QueryTxLatestToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletSrvServer).QueryTxLatestToken(ctx, req.(*QueryTxLatestTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WalletSrv_ReportInternalTxn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportInternalTxnReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletSrvServer).ReportInternalTxn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WalletSrv_ReportInternalTxn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletSrvServer).ReportInternalTxn(ctx, req.(*ReportInternalTxnReq))
	}
	return interceptor(ctx, in, info, handler)
}

// WalletSrv_ServiceDesc is the grpc.ServiceDesc for WalletSrv service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WalletSrv_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.wallet.v1.WalletSrv",
	HandlerType: (*WalletSrvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "NetworkList",
			Handler:    _WalletSrv_NetworkList_Handler,
		},
		{
			MethodName: "Transactions",
			Handler:    _WalletSrv_Transactions_Handler,
		},
		{
			MethodName: "TransactionsByAddress",
			Handler:    _WalletSrv_TransactionsByAddress_Handler,
		},
		{
			MethodName: "TransactionInfo",
			Handler:    _WalletSrv_TransactionInfo_Handler,
		},
		{
			MethodName: "TokenList",
			Handler:    _WalletSrv_TokenList_Handler,
		},
		{
			MethodName: "GetToken",
			Handler:    _WalletSrv_GetToken_Handler,
		},
		{
			MethodName: "ListTokenBalance",
			Handler:    _WalletSrv_ListTokenBalance_Handler,
		},
		{
			MethodName: "QueryTxLatestToken",
			Handler:    _WalletSrv_QueryTxLatestToken_Handler,
		},
		{
			MethodName: "ReportInternalTxn",
			Handler:    _WalletSrv_ReportInternalTxn_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/wallet/v1/wallet.proto",
}
