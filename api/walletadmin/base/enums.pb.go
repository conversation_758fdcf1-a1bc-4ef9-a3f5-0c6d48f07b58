// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/walletadmin/base/enums.proto

package base

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EnableStatus int32

const (
	EnableStatus_ENABLE_STATUS_UNSPECIFIED EnableStatus = 0
	// 启用
	EnableStatus_ENABLE_STATUS_ENABLE EnableStatus = 1
	// 禁用
	EnableStatus_ENABLE_STATUS_DISABLE EnableStatus = 2
)

// Enum value maps for EnableStatus.
var (
	EnableStatus_name = map[int32]string{
		0: "ENABLE_STATUS_UNSPECIFIED",
		1: "ENABLE_STATUS_ENABLE",
		2: "ENABLE_STATUS_DISABLE",
	}
	EnableStatus_value = map[string]int32{
		"ENABLE_STATUS_UNSPECIFIED": 0,
		"ENABLE_STATUS_ENABLE":      1,
		"ENABLE_STATUS_DISABLE":     2,
	}
)

func (x EnableStatus) Enum() *EnableStatus {
	p := new(EnableStatus)
	*p = x
	return p
}

func (x EnableStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnableStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_walletadmin_base_enums_proto_enumTypes[0].Descriptor()
}

func (EnableStatus) Type() protoreflect.EnumType {
	return &file_api_walletadmin_base_enums_proto_enumTypes[0]
}

func (x EnableStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnableStatus.Descriptor instead.
func (EnableStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_walletadmin_base_enums_proto_rawDescGZIP(), []int{0}
}

var File_api_walletadmin_base_enums_proto protoreflect.FileDescriptor

const file_api_walletadmin_base_enums_proto_rawDesc = "" +
	"\n" +
	" api/walletadmin/base/enums.proto\x12\x14api.walletadmin.base*b\n" +
	"\fEnableStatus\x12\x1d\n" +
	"\x19ENABLE_STATUS_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14ENABLE_STATUS_ENABLE\x10\x01\x12\x19\n" +
	"\x15ENABLE_STATUS_DISABLE\x10\x02B\xbe\x01\n" +
	"\x18com.api.walletadmin.baseB\n" +
	"EnumsProtoP\x01Z$byd_wallet/api/walletadmin/base;base\xa2\x02\x03AWB\xaa\x02\x14Api.Walletadmin.Base\xca\x02\x14Api\\Walletadmin\\Base\xe2\x02 Api\\Walletadmin\\Base\\GPBMetadata\xea\x02\x16Api::Walletadmin::Baseb\x06proto3"

var (
	file_api_walletadmin_base_enums_proto_rawDescOnce sync.Once
	file_api_walletadmin_base_enums_proto_rawDescData []byte
)

func file_api_walletadmin_base_enums_proto_rawDescGZIP() []byte {
	file_api_walletadmin_base_enums_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_base_enums_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_walletadmin_base_enums_proto_rawDesc), len(file_api_walletadmin_base_enums_proto_rawDesc)))
	})
	return file_api_walletadmin_base_enums_proto_rawDescData
}

var file_api_walletadmin_base_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_walletadmin_base_enums_proto_goTypes = []any{
	(EnableStatus)(0), // 0: api.walletadmin.base.EnableStatus
}
var file_api_walletadmin_base_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_walletadmin_base_enums_proto_init() }
func file_api_walletadmin_base_enums_proto_init() {
	if File_api_walletadmin_base_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_walletadmin_base_enums_proto_rawDesc), len(file_api_walletadmin_base_enums_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_walletadmin_base_enums_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_base_enums_proto_depIdxs,
		EnumInfos:         file_api_walletadmin_base_enums_proto_enumTypes,
	}.Build()
	File_api_walletadmin_base_enums_proto = out.File
	file_api_walletadmin_base_enums_proto_goTypes = nil
	file_api_walletadmin_base_enums_proto_depIdxs = nil
}
