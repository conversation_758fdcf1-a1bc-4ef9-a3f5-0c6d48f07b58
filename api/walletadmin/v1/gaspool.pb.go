// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/walletadmin/v1/gaspool.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	base "byd_wallet/api/walletadmin/base"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateGasPoolDepositTokenReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 是否启用(1:启用,2:禁用)
	Enable        base.EnableStatus `protobuf:"varint,2,opt,name=enable,proto3,enum=api.walletadmin.base.EnableStatus" json:"enable,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateGasPoolDepositTokenReq) Reset() {
	*x = UpdateGasPoolDepositTokenReq{}
	mi := &file_api_walletadmin_v1_gaspool_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateGasPoolDepositTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGasPoolDepositTokenReq) ProtoMessage() {}

func (x *UpdateGasPoolDepositTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_gaspool_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGasPoolDepositTokenReq.ProtoReflect.Descriptor instead.
func (*UpdateGasPoolDepositTokenReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_gaspool_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateGasPoolDepositTokenReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateGasPoolDepositTokenReq) GetEnable() base.EnableStatus {
	if x != nil {
		return x.Enable
	}
	return base.EnableStatus(0)
}

type GasPoolDepositToken struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 币名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 币符号
	Symbol string `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 币精度
	Decimals int64 `protobuf:"varint,4,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// 币图标
	LogoUrl string `protobuf:"bytes,5,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// 币地址
	Address string `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,7,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 链名称
	ChainName string `protobuf:"bytes,8,opt,name=chain_name,json=chainName,proto3" json:"chain_name,omitempty"`
	// 最小充值金额(不带精度)
	MinDepositAmount string `protobuf:"bytes,9,opt,name=min_deposit_amount,json=minDepositAmount,proto3" json:"min_deposit_amount,omitempty"`
	// 是否启用
	Enable        bool `protobuf:"varint,10,opt,name=enable,proto3" json:"enable,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GasPoolDepositToken) Reset() {
	*x = GasPoolDepositToken{}
	mi := &file_api_walletadmin_v1_gaspool_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GasPoolDepositToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GasPoolDepositToken) ProtoMessage() {}

func (x *GasPoolDepositToken) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_gaspool_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GasPoolDepositToken.ProtoReflect.Descriptor instead.
func (*GasPoolDepositToken) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_gaspool_proto_rawDescGZIP(), []int{1}
}

func (x *GasPoolDepositToken) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GasPoolDepositToken) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GasPoolDepositToken) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *GasPoolDepositToken) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *GasPoolDepositToken) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *GasPoolDepositToken) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *GasPoolDepositToken) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *GasPoolDepositToken) GetChainName() string {
	if x != nil {
		return x.ChainName
	}
	return ""
}

func (x *GasPoolDepositToken) GetMinDepositAmount() string {
	if x != nil {
		return x.MinDepositAmount
	}
	return ""
}

func (x *GasPoolDepositToken) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type ListGasPoolDepositTokenReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGasPoolDepositTokenReq) Reset() {
	*x = ListGasPoolDepositTokenReq{}
	mi := &file_api_walletadmin_v1_gaspool_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGasPoolDepositTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGasPoolDepositTokenReq) ProtoMessage() {}

func (x *ListGasPoolDepositTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_gaspool_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGasPoolDepositTokenReq.ProtoReflect.Descriptor instead.
func (*ListGasPoolDepositTokenReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_gaspool_proto_rawDescGZIP(), []int{2}
}

type ListGasPoolDepositTokenReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*GasPoolDepositToken `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGasPoolDepositTokenReply) Reset() {
	*x = ListGasPoolDepositTokenReply{}
	mi := &file_api_walletadmin_v1_gaspool_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGasPoolDepositTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGasPoolDepositTokenReply) ProtoMessage() {}

func (x *ListGasPoolDepositTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_gaspool_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGasPoolDepositTokenReply.ProtoReflect.Descriptor instead.
func (*ListGasPoolDepositTokenReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_gaspool_proto_rawDescGZIP(), []int{3}
}

func (x *ListGasPoolDepositTokenReply) GetList() []*GasPoolDepositToken {
	if x != nil {
		return x.List
	}
	return nil
}

var File_api_walletadmin_v1_gaspool_proto protoreflect.FileDescriptor

const file_api_walletadmin_v1_gaspool_proto_rawDesc = "" +
	"\n" +
	" api/walletadmin/v1/gaspool.proto\x12\x12api.walletadmin.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1bbuf/validate/validate.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a api/walletadmin/base/enums.proto\"}\n" +
	"\x1cUpdateGasPoolDepositTokenReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\x12D\n" +
	"\x06enable\x18\x02 \x01(\x0e2\".api.walletadmin.base.EnableStatusB\b\xbaH\x05\x82\x01\x02 \x00R\x06enable\"\xa8\x02\n" +
	"\x13GasPoolDepositToken\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06symbol\x18\x03 \x01(\tR\x06symbol\x12\x1a\n" +
	"\bdecimals\x18\x04 \x01(\x03R\bdecimals\x12\x19\n" +
	"\blogo_url\x18\x05 \x01(\tR\alogoUrl\x12\x18\n" +
	"\aaddress\x18\x06 \x01(\tR\aaddress\x12\x1f\n" +
	"\vchain_index\x18\a \x01(\x03R\n" +
	"chainIndex\x12\x1d\n" +
	"\n" +
	"chain_name\x18\b \x01(\tR\tchainName\x12,\n" +
	"\x12min_deposit_amount\x18\t \x01(\tR\x10minDepositAmount\x12\x16\n" +
	"\x06enable\x18\n" +
	" \x01(\bR\x06enable\"\x1c\n" +
	"\x1aListGasPoolDepositTokenReq\"[\n" +
	"\x1cListGasPoolDepositTokenReply\x12;\n" +
	"\x04list\x18\x01 \x03(\v2'.api.walletadmin.v1.GasPoolDepositTokenR\x04list2\xce\x02\n" +
	"\n" +
	"GasPoolSrv\x12\xa5\x01\n" +
	"\x17ListGasPoolDepositToken\x12..api.walletadmin.v1.ListGasPoolDepositTokenReq\x1a0.api.walletadmin.v1.ListGasPoolDepositTokenReply\"(\x82\xd3\xe4\x93\x02\"\x12 /admin/v1/gaspool/deposit_tokens\x12\x97\x01\n" +
	"\x19UpdateGasPoolDepositToken\x120.api.walletadmin.v1.UpdateGasPoolDepositTokenReq\x1a\x16.google.protobuf.Empty\"0\x82\xd3\xe4\x93\x02*:\x01*\x1a%/admin/v1/gaspool/deposit_tokens/{id}B\xb2\x01\n" +
	"\x16com.api.walletadmin.v1B\fGaspoolProtoP\x01Z byd_wallet/api/walletadmin/v1;v1\xa2\x02\x03AWX\xaa\x02\x12Api.Walletadmin.V1\xca\x02\x12Api\\Walletadmin\\V1\xe2\x02\x1eApi\\Walletadmin\\V1\\GPBMetadata\xea\x02\x14Api::Walletadmin::V1b\x06proto3"

var (
	file_api_walletadmin_v1_gaspool_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_gaspool_proto_rawDescData []byte
)

func file_api_walletadmin_v1_gaspool_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_gaspool_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_gaspool_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_gaspool_proto_rawDesc), len(file_api_walletadmin_v1_gaspool_proto_rawDesc)))
	})
	return file_api_walletadmin_v1_gaspool_proto_rawDescData
}

var file_api_walletadmin_v1_gaspool_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_walletadmin_v1_gaspool_proto_goTypes = []any{
	(*UpdateGasPoolDepositTokenReq)(nil), // 0: api.walletadmin.v1.UpdateGasPoolDepositTokenReq
	(*GasPoolDepositToken)(nil),          // 1: api.walletadmin.v1.GasPoolDepositToken
	(*ListGasPoolDepositTokenReq)(nil),   // 2: api.walletadmin.v1.ListGasPoolDepositTokenReq
	(*ListGasPoolDepositTokenReply)(nil), // 3: api.walletadmin.v1.ListGasPoolDepositTokenReply
	(base.EnableStatus)(0),               // 4: api.walletadmin.base.EnableStatus
	(*emptypb.Empty)(nil),                // 5: google.protobuf.Empty
}
var file_api_walletadmin_v1_gaspool_proto_depIdxs = []int32{
	4, // 0: api.walletadmin.v1.UpdateGasPoolDepositTokenReq.enable:type_name -> api.walletadmin.base.EnableStatus
	1, // 1: api.walletadmin.v1.ListGasPoolDepositTokenReply.list:type_name -> api.walletadmin.v1.GasPoolDepositToken
	2, // 2: api.walletadmin.v1.GasPoolSrv.ListGasPoolDepositToken:input_type -> api.walletadmin.v1.ListGasPoolDepositTokenReq
	0, // 3: api.walletadmin.v1.GasPoolSrv.UpdateGasPoolDepositToken:input_type -> api.walletadmin.v1.UpdateGasPoolDepositTokenReq
	3, // 4: api.walletadmin.v1.GasPoolSrv.ListGasPoolDepositToken:output_type -> api.walletadmin.v1.ListGasPoolDepositTokenReply
	5, // 5: api.walletadmin.v1.GasPoolSrv.UpdateGasPoolDepositToken:output_type -> google.protobuf.Empty
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_gaspool_proto_init() }
func file_api_walletadmin_v1_gaspool_proto_init() {
	if File_api_walletadmin_v1_gaspool_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_gaspool_proto_rawDesc), len(file_api_walletadmin_v1_gaspool_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_walletadmin_v1_gaspool_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_gaspool_proto_depIdxs,
		MessageInfos:      file_api_walletadmin_v1_gaspool_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_gaspool_proto = out.File
	file_api_walletadmin_v1_gaspool_proto_goTypes = nil
	file_api_walletadmin_v1_gaspool_proto_depIdxs = nil
}
