syntax = "proto3";

package api.walletadmin.v1;

option go_package = "byd_wallet/api/walletadmin/v1;v1";

import "google/api/annotations.proto";
import "buf/validate/validate.proto";
import "google/protobuf/empty.proto";
import "api/walletadmin/base/enums.proto";

service GasPoolSrv {
  // 可充值币种列表
  rpc ListGasPoolDepositToken(ListGasPoolDepositTokenReq) returns (ListGasPoolDepositTokenReply) {
    option (google.api.http) = {
      get: "/admin/v1/gaspool/deposit_tokens"
    };
  }
  // 更新充值币种
  rpc UpdateGasPoolDepositToken(UpdateGasPoolDepositTokenReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/admin/v1/gaspool/deposit_tokens/{id}"
      body: "*"
    };
  }
}

message UpdateGasPoolDepositTokenReq {
  // 记录ID
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
  // 是否启用(1:启用,2:禁用)
  api.walletadmin.base.EnableStatus enable = 2 [(buf.validate.field).enum = { not_in: [0]}];
}

message GasPoolDepositToken {
  // 记录ID
  uint64 id = 1;
  // 币名称
  string name = 2;
  // 币符号
  string symbol = 3;
  // 币精度
  int64 decimals = 4;
  // 币图标
  string logo_url = 5;
  // 币地址
  string address = 6;
  // 链索引
  int64 chain_index = 7;
  // 链名称
  string chain_name = 8;
  // 最小充值金额(不带精度)
  string min_deposit_amount = 9;
  // 是否启用
  bool enable = 10;
}

message ListGasPoolDepositTokenReq {}

message ListGasPoolDepositTokenReply {
  repeated GasPoolDepositToken list = 1;
}