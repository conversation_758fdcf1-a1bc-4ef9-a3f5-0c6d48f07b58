// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/walletadmin/v1/gaspool.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	GasPoolSrv_ListGasPoolDepositToken_FullMethodName   = "/api.walletadmin.v1.GasPoolSrv/ListGasPoolDepositToken"
	GasPoolSrv_UpdateGasPoolDepositToken_FullMethodName = "/api.walletadmin.v1.GasPoolSrv/UpdateGasPoolDepositToken"
)

// GasPoolSrvClient is the client API for GasPoolSrv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GasPoolSrvClient interface {
	// 可充值币种列表
	ListGasPoolDepositToken(ctx context.Context, in *ListGasPoolDepositTokenReq, opts ...grpc.CallOption) (*ListGasPoolDepositTokenReply, error)
	// 更新充值币种
	UpdateGasPoolDepositToken(ctx context.Context, in *UpdateGasPoolDepositTokenReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type gasPoolSrvClient struct {
	cc grpc.ClientConnInterface
}

func NewGasPoolSrvClient(cc grpc.ClientConnInterface) GasPoolSrvClient {
	return &gasPoolSrvClient{cc}
}

func (c *gasPoolSrvClient) ListGasPoolDepositToken(ctx context.Context, in *ListGasPoolDepositTokenReq, opts ...grpc.CallOption) (*ListGasPoolDepositTokenReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListGasPoolDepositTokenReply)
	err := c.cc.Invoke(ctx, GasPoolSrv_ListGasPoolDepositToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gasPoolSrvClient) UpdateGasPoolDepositToken(ctx context.Context, in *UpdateGasPoolDepositTokenReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, GasPoolSrv_UpdateGasPoolDepositToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GasPoolSrvServer is the server API for GasPoolSrv service.
// All implementations must embed UnimplementedGasPoolSrvServer
// for forward compatibility.
type GasPoolSrvServer interface {
	// 可充值币种列表
	ListGasPoolDepositToken(context.Context, *ListGasPoolDepositTokenReq) (*ListGasPoolDepositTokenReply, error)
	// 更新充值币种
	UpdateGasPoolDepositToken(context.Context, *UpdateGasPoolDepositTokenReq) (*emptypb.Empty, error)
	mustEmbedUnimplementedGasPoolSrvServer()
}

// UnimplementedGasPoolSrvServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedGasPoolSrvServer struct{}

func (UnimplementedGasPoolSrvServer) ListGasPoolDepositToken(context.Context, *ListGasPoolDepositTokenReq) (*ListGasPoolDepositTokenReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGasPoolDepositToken not implemented")
}
func (UnimplementedGasPoolSrvServer) UpdateGasPoolDepositToken(context.Context, *UpdateGasPoolDepositTokenReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGasPoolDepositToken not implemented")
}
func (UnimplementedGasPoolSrvServer) mustEmbedUnimplementedGasPoolSrvServer() {}
func (UnimplementedGasPoolSrvServer) testEmbeddedByValue()                    {}

// UnsafeGasPoolSrvServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GasPoolSrvServer will
// result in compilation errors.
type UnsafeGasPoolSrvServer interface {
	mustEmbedUnimplementedGasPoolSrvServer()
}

func RegisterGasPoolSrvServer(s grpc.ServiceRegistrar, srv GasPoolSrvServer) {
	// If the following call pancis, it indicates UnimplementedGasPoolSrvServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&GasPoolSrv_ServiceDesc, srv)
}

func _GasPoolSrv_ListGasPoolDepositToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListGasPoolDepositTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GasPoolSrvServer).ListGasPoolDepositToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GasPoolSrv_ListGasPoolDepositToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GasPoolSrvServer).ListGasPoolDepositToken(ctx, req.(*ListGasPoolDepositTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GasPoolSrv_UpdateGasPoolDepositToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGasPoolDepositTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GasPoolSrvServer).UpdateGasPoolDepositToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GasPoolSrv_UpdateGasPoolDepositToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GasPoolSrvServer).UpdateGasPoolDepositToken(ctx, req.(*UpdateGasPoolDepositTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

// GasPoolSrv_ServiceDesc is the grpc.ServiceDesc for GasPoolSrv service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GasPoolSrv_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.walletadmin.v1.GasPoolSrv",
	HandlerType: (*GasPoolSrvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListGasPoolDepositToken",
			Handler:    _GasPoolSrv_ListGasPoolDepositToken_Handler,
		},
		{
			MethodName: "UpdateGasPoolDepositToken",
			Handler:    _GasPoolSrv_UpdateGasPoolDepositToken_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/walletadmin/v1/gaspool.proto",
}
