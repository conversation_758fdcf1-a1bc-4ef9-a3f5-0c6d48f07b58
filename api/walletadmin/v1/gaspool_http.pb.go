// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: api/walletadmin/v1/gaspool.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationGasPoolSrvListGasPoolDepositToken = "/api.walletadmin.v1.GasPoolSrv/ListGasPoolDepositToken"
const OperationGasPoolSrvUpdateGasPoolDepositToken = "/api.walletadmin.v1.GasPoolSrv/UpdateGasPoolDepositToken"

type GasPoolSrvHTTPServer interface {
	// ListGasPoolDepositToken 可充值币种列表
	ListGasPoolDepositToken(context.Context, *ListGasPoolDepositTokenReq) (*ListGasPoolDepositTokenReply, error)
	// UpdateGasPoolDepositToken 更新充值币种
	UpdateGasPoolDepositToken(context.Context, *UpdateGasPoolDepositTokenReq) (*emptypb.Empty, error)
}

func RegisterGasPoolSrvHTTPServer(s *http.Server, srv GasPoolSrvHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/gaspool/deposit_tokens", _GasPoolSrv_ListGasPoolDepositToken0_HTTP_Handler(srv))
	r.PUT("/admin/v1/gaspool/deposit_tokens/{id}", _GasPoolSrv_UpdateGasPoolDepositToken0_HTTP_Handler(srv))
}

func _GasPoolSrv_ListGasPoolDepositToken0_HTTP_Handler(srv GasPoolSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListGasPoolDepositTokenReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGasPoolSrvListGasPoolDepositToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListGasPoolDepositToken(ctx, req.(*ListGasPoolDepositTokenReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListGasPoolDepositTokenReply)
		return ctx.Result(200, reply)
	}
}

func _GasPoolSrv_UpdateGasPoolDepositToken0_HTTP_Handler(srv GasPoolSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateGasPoolDepositTokenReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGasPoolSrvUpdateGasPoolDepositToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateGasPoolDepositToken(ctx, req.(*UpdateGasPoolDepositTokenReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type GasPoolSrvHTTPClient interface {
	ListGasPoolDepositToken(ctx context.Context, req *ListGasPoolDepositTokenReq, opts ...http.CallOption) (rsp *ListGasPoolDepositTokenReply, err error)
	UpdateGasPoolDepositToken(ctx context.Context, req *UpdateGasPoolDepositTokenReq, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type GasPoolSrvHTTPClientImpl struct {
	cc *http.Client
}

func NewGasPoolSrvHTTPClient(client *http.Client) GasPoolSrvHTTPClient {
	return &GasPoolSrvHTTPClientImpl{client}
}

func (c *GasPoolSrvHTTPClientImpl) ListGasPoolDepositToken(ctx context.Context, in *ListGasPoolDepositTokenReq, opts ...http.CallOption) (*ListGasPoolDepositTokenReply, error) {
	var out ListGasPoolDepositTokenReply
	pattern := "/admin/v1/gaspool/deposit_tokens"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationGasPoolSrvListGasPoolDepositToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GasPoolSrvHTTPClientImpl) UpdateGasPoolDepositToken(ctx context.Context, in *UpdateGasPoolDepositTokenReq, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/gaspool/deposit_tokens/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGasPoolSrvUpdateGasPoolDepositToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
