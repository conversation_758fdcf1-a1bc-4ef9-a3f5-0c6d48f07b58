// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/walletadmin/v1/voucher.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VoucherRecord struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 网络
	ChainName string `protobuf:"bytes,2,opt,name=chain_name,json=chainName,proto3" json:"chain_name,omitempty"`
	// 币种
	Symbol string `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 合约地址
	TokenAddress string `protobuf:"bytes,4,opt,name=token_address,json=tokenAddress,proto3" json:"token_address,omitempty"`
	// 转账数量
	Value string `protobuf:"bytes,5,opt,name=value,proto3" json:"value,omitempty"`
	// 转出地址
	FromAddress string `protobuf:"bytes,6,opt,name=from_address,json=fromAddress,proto3" json:"from_address,omitempty"`
	// 转入地址
	ToAddress string `protobuf:"bytes,7,opt,name=to_address,json=toAddress,proto3" json:"to_address,omitempty"`
	// 交易时间
	Timestamp int64 `protobuf:"varint,8,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// 状态 init pending success fail
	Status string `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"`
	// token精度
	Decimals int64 `protobuf:"varint,10,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// 链索引
	ChainIndex    int64 `protobuf:"varint,11,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VoucherRecord) Reset() {
	*x = VoucherRecord{}
	mi := &file_api_walletadmin_v1_voucher_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VoucherRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoucherRecord) ProtoMessage() {}

func (x *VoucherRecord) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_voucher_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoucherRecord.ProtoReflect.Descriptor instead.
func (*VoucherRecord) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_voucher_proto_rawDescGZIP(), []int{0}
}

func (x *VoucherRecord) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VoucherRecord) GetChainName() string {
	if x != nil {
		return x.ChainName
	}
	return ""
}

func (x *VoucherRecord) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *VoucherRecord) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

func (x *VoucherRecord) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *VoucherRecord) GetFromAddress() string {
	if x != nil {
		return x.FromAddress
	}
	return ""
}

func (x *VoucherRecord) GetToAddress() string {
	if x != nil {
		return x.ToAddress
	}
	return ""
}

func (x *VoucherRecord) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *VoucherRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *VoucherRecord) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *VoucherRecord) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

type ListVoucherRecordReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 页码 从1开始
	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页展示条数
	PageSize int64 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,3,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 转出地址
	FromAddress string `protobuf:"bytes,4,opt,name=from_address,json=fromAddress,proto3" json:"from_address,omitempty"`
	// 转入地址
	ToAddress     string `protobuf:"bytes,5,opt,name=to_address,json=toAddress,proto3" json:"to_address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListVoucherRecordReq) Reset() {
	*x = ListVoucherRecordReq{}
	mi := &file_api_walletadmin_v1_voucher_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListVoucherRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVoucherRecordReq) ProtoMessage() {}

func (x *ListVoucherRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_voucher_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVoucherRecordReq.ProtoReflect.Descriptor instead.
func (*ListVoucherRecordReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_voucher_proto_rawDescGZIP(), []int{1}
}

func (x *ListVoucherRecordReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListVoucherRecordReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListVoucherRecordReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *ListVoucherRecordReq) GetFromAddress() string {
	if x != nil {
		return x.FromAddress
	}
	return ""
}

func (x *ListVoucherRecordReq) GetToAddress() string {
	if x != nil {
		return x.ToAddress
	}
	return ""
}

type ListVoucherRecordReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 兑换记录
	List []*VoucherRecord `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总条数
	TotalCount    int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListVoucherRecordReply) Reset() {
	*x = ListVoucherRecordReply{}
	mi := &file_api_walletadmin_v1_voucher_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListVoucherRecordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVoucherRecordReply) ProtoMessage() {}

func (x *ListVoucherRecordReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_voucher_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVoucherRecordReply.ProtoReflect.Descriptor instead.
func (*ListVoucherRecordReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_voucher_proto_rawDescGZIP(), []int{2}
}

func (x *ListVoucherRecordReply) GetList() []*VoucherRecord {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListVoucherRecordReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type VoucherStatsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VoucherStatsReq) Reset() {
	*x = VoucherStatsReq{}
	mi := &file_api_walletadmin_v1_voucher_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VoucherStatsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoucherStatsReq) ProtoMessage() {}

func (x *VoucherStatsReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_voucher_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoucherStatsReq.ProtoReflect.Descriptor instead.
func (*VoucherStatsReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_voucher_proto_rawDescGZIP(), []int{3}
}

type VoucherStatsReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 统计币种
	StatsSymbol string `protobuf:"bytes,1,opt,name=stats_symbol,json=statsSymbol,proto3" json:"stats_symbol,omitempty"`
	// token精度
	Decimals int64 `protobuf:"varint,2,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// 今日转出
	ValueOfToday string `protobuf:"bytes,3,opt,name=value_of_today,json=valueOfToday,proto3" json:"value_of_today,omitempty"`
	// 今日兑换笔数
	CountOfToday int64 `protobuf:"varint,4,opt,name=count_of_today,json=countOfToday,proto3" json:"count_of_today,omitempty"`
	// 历史转出
	TotalValue string `protobuf:"bytes,5,opt,name=total_value,json=totalValue,proto3" json:"total_value,omitempty"`
	// 历史兑换笔数
	TotalCount    int64 `protobuf:"varint,6,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VoucherStatsReply) Reset() {
	*x = VoucherStatsReply{}
	mi := &file_api_walletadmin_v1_voucher_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VoucherStatsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoucherStatsReply) ProtoMessage() {}

func (x *VoucherStatsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_voucher_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoucherStatsReply.ProtoReflect.Descriptor instead.
func (*VoucherStatsReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_voucher_proto_rawDescGZIP(), []int{4}
}

func (x *VoucherStatsReply) GetStatsSymbol() string {
	if x != nil {
		return x.StatsSymbol
	}
	return ""
}

func (x *VoucherStatsReply) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *VoucherStatsReply) GetValueOfToday() string {
	if x != nil {
		return x.ValueOfToday
	}
	return ""
}

func (x *VoucherStatsReply) GetCountOfToday() int64 {
	if x != nil {
		return x.CountOfToday
	}
	return 0
}

func (x *VoucherStatsReply) GetTotalValue() string {
	if x != nil {
		return x.TotalValue
	}
	return ""
}

func (x *VoucherStatsReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

var File_api_walletadmin_v1_voucher_proto protoreflect.FileDescriptor

const file_api_walletadmin_v1_voucher_proto_rawDesc = "" +
	"\n" +
	" api/walletadmin/v1/voucher.proto\x12\x12api.walletadmin.v1\x1a\x1bbuf/validate/validate.proto\x1a\x1cgoogle/api/annotations.proto\"\xc6\x02\n" +
	"\rVoucherRecord\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1d\n" +
	"\n" +
	"chain_name\x18\x02 \x01(\tR\tchainName\x12\x16\n" +
	"\x06symbol\x18\x03 \x01(\tR\x06symbol\x12#\n" +
	"\rtoken_address\x18\x04 \x01(\tR\ftokenAddress\x12\x14\n" +
	"\x05value\x18\x05 \x01(\tR\x05value\x12!\n" +
	"\ffrom_address\x18\x06 \x01(\tR\vfromAddress\x12\x1d\n" +
	"\n" +
	"to_address\x18\a \x01(\tR\ttoAddress\x12\x1c\n" +
	"\ttimestamp\x18\b \x01(\x03R\ttimestamp\x12\x16\n" +
	"\x06status\x18\t \x01(\tR\x06status\x12\x1a\n" +
	"\bdecimals\x18\n" +
	" \x01(\x03R\bdecimals\x12\x1f\n" +
	"\vchain_index\x18\v \x01(\x03R\n" +
	"chainIndex\"\xbe\x01\n" +
	"\x14ListVoucherRecordReq\x12\x1b\n" +
	"\x04page\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x04page\x12&\n" +
	"\tpage_size\x18\x02 \x01(\x03B\t\xbaH\x06\"\x04\x18d \x00R\bpageSize\x12\x1f\n" +
	"\vchain_index\x18\x03 \x01(\x03R\n" +
	"chainIndex\x12!\n" +
	"\ffrom_address\x18\x04 \x01(\tR\vfromAddress\x12\x1d\n" +
	"\n" +
	"to_address\x18\x05 \x01(\tR\ttoAddress\"p\n" +
	"\x16ListVoucherRecordReply\x125\n" +
	"\x04list\x18\x01 \x03(\v2!.api.walletadmin.v1.VoucherRecordR\x04list\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x03R\n" +
	"totalCount\"\x11\n" +
	"\x0fVoucherStatsReq\"\xe0\x01\n" +
	"\x11VoucherStatsReply\x12!\n" +
	"\fstats_symbol\x18\x01 \x01(\tR\vstatsSymbol\x12\x1a\n" +
	"\bdecimals\x18\x02 \x01(\x03R\bdecimals\x12$\n" +
	"\x0evalue_of_today\x18\x03 \x01(\tR\fvalueOfToday\x12$\n" +
	"\x0ecount_of_today\x18\x04 \x01(\x03R\fcountOfToday\x12\x1f\n" +
	"\vtotal_value\x18\x05 \x01(\tR\n" +
	"totalValue\x12\x1f\n" +
	"\vtotal_count\x18\x06 \x01(\x03R\n" +
	"totalCount2\x9b\x02\n" +
	"\x0eVoucherService\x12\x8b\x01\n" +
	"\x11ListVoucherRecord\x12(.api.walletadmin.v1.ListVoucherRecordReq\x1a*.api.walletadmin.v1.ListVoucherRecordReply\" \x82\xd3\xe4\x93\x02\x1a\x12\x18/admin/v1/voucher/record\x12{\n" +
	"\fVoucherStats\x12#.api.walletadmin.v1.VoucherStatsReq\x1a%.api.walletadmin.v1.VoucherStatsReply\"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/admin/v1/voucher/statsB\xb2\x01\n" +
	"\x16com.api.walletadmin.v1B\fVoucherProtoP\x01Z byd_wallet/api/walletadmin/v1;v1\xa2\x02\x03AWX\xaa\x02\x12Api.Walletadmin.V1\xca\x02\x12Api\\Walletadmin\\V1\xe2\x02\x1eApi\\Walletadmin\\V1\\GPBMetadata\xea\x02\x14Api::Walletadmin::V1b\x06proto3"

var (
	file_api_walletadmin_v1_voucher_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_voucher_proto_rawDescData []byte
)

func file_api_walletadmin_v1_voucher_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_voucher_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_voucher_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_voucher_proto_rawDesc), len(file_api_walletadmin_v1_voucher_proto_rawDesc)))
	})
	return file_api_walletadmin_v1_voucher_proto_rawDescData
}

var file_api_walletadmin_v1_voucher_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_walletadmin_v1_voucher_proto_goTypes = []any{
	(*VoucherRecord)(nil),          // 0: api.walletadmin.v1.VoucherRecord
	(*ListVoucherRecordReq)(nil),   // 1: api.walletadmin.v1.ListVoucherRecordReq
	(*ListVoucherRecordReply)(nil), // 2: api.walletadmin.v1.ListVoucherRecordReply
	(*VoucherStatsReq)(nil),        // 3: api.walletadmin.v1.VoucherStatsReq
	(*VoucherStatsReply)(nil),      // 4: api.walletadmin.v1.VoucherStatsReply
}
var file_api_walletadmin_v1_voucher_proto_depIdxs = []int32{
	0, // 0: api.walletadmin.v1.ListVoucherRecordReply.list:type_name -> api.walletadmin.v1.VoucherRecord
	1, // 1: api.walletadmin.v1.VoucherService.ListVoucherRecord:input_type -> api.walletadmin.v1.ListVoucherRecordReq
	3, // 2: api.walletadmin.v1.VoucherService.VoucherStats:input_type -> api.walletadmin.v1.VoucherStatsReq
	2, // 3: api.walletadmin.v1.VoucherService.ListVoucherRecord:output_type -> api.walletadmin.v1.ListVoucherRecordReply
	4, // 4: api.walletadmin.v1.VoucherService.VoucherStats:output_type -> api.walletadmin.v1.VoucherStatsReply
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_voucher_proto_init() }
func file_api_walletadmin_v1_voucher_proto_init() {
	if File_api_walletadmin_v1_voucher_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_voucher_proto_rawDesc), len(file_api_walletadmin_v1_voucher_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_walletadmin_v1_voucher_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_voucher_proto_depIdxs,
		MessageInfos:      file_api_walletadmin_v1_voucher_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_voucher_proto = out.File
	file_api_walletadmin_v1_voucher_proto_goTypes = nil
	file_api_walletadmin_v1_voucher_proto_depIdxs = nil
}
