syntax = "proto3";

package api.walletadmin.v1;

import "buf/validate/validate.proto";
import "google/api/annotations.proto";

option go_package = "byd_wallet/api/walletadmin/v1;v1";

service VoucherService {
  // 兑换记录列表
  rpc ListVoucherRecord (ListVoucherRecordReq) returns (ListVoucherRecordReply) {
    option (google.api.http) = {
      get: "/admin/v1/voucher/record"
    };
  }
  // 兑换统计
  rpc VoucherStats (VoucherStatsReq) returns (VoucherStatsReply) {
    option (google.api.http) = {
      get: "/admin/v1/voucher/stats"
    };
  }
}

message VoucherRecord {
  // 记录id
  uint64 id = 1;
  // 网络
  string chain_name = 2;
  // 币种
  string symbol = 3;
  // 合约地址
  string token_address = 4;
  // 转账数量
  string value = 5;
  // 转出地址
  string from_address = 6;
  // 转入地址
  string to_address = 7;
  // 交易时间
  int64 timestamp = 8;
  // 状态 init pending success fail
  string status = 9;
  // token精度
  int64 decimals = 10;
  // 链索引
  int64 chain_index = 11;
}

message ListVoucherRecordReq {
  // 页码 从1开始
  int64 page = 1 [(buf.validate.field).int64.gt = 0];
  // 每页展示条数
  int64 page_size = 2 [(buf.validate.field).int64 = {
    gt: 0
    lte: 100
  }];
  // 链索引
  int64 chain_index = 3;
  // 转出地址
  string from_address = 4;
  // 转入地址
  string to_address = 5;
}

message ListVoucherRecordReply {
  // 兑换记录
  repeated VoucherRecord list = 1;
  // 总条数
  int64 total_count = 2;
}

message VoucherStatsReq {}

message VoucherStatsReply {
  // 统计币种
  string stats_symbol = 1;
  // token精度
  int64 decimals = 2;
  // 今日转出
  string value_of_today = 3;
  // 今日兑换笔数
  int64 count_of_today = 4;
  // 历史转出
  string total_value = 5;
  // 历史兑换笔数
  int64 total_count = 6;
}

