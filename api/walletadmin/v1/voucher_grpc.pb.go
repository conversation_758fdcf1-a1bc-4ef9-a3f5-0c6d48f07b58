// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/walletadmin/v1/voucher.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	VoucherService_ListVoucherRecord_FullMethodName = "/api.walletadmin.v1.VoucherService/ListVoucherRecord"
	VoucherService_VoucherStats_FullMethodName      = "/api.walletadmin.v1.VoucherService/VoucherStats"
)

// VoucherServiceClient is the client API for VoucherService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VoucherServiceClient interface {
	// 兑换记录列表
	ListVoucherRecord(ctx context.Context, in *ListVoucherRecordReq, opts ...grpc.CallOption) (*ListVoucherRecordReply, error)
	// 兑换统计
	VoucherStats(ctx context.Context, in *VoucherStatsReq, opts ...grpc.CallOption) (*VoucherStatsReply, error)
}

type voucherServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewVoucherServiceClient(cc grpc.ClientConnInterface) VoucherServiceClient {
	return &voucherServiceClient{cc}
}

func (c *voucherServiceClient) ListVoucherRecord(ctx context.Context, in *ListVoucherRecordReq, opts ...grpc.CallOption) (*ListVoucherRecordReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListVoucherRecordReply)
	err := c.cc.Invoke(ctx, VoucherService_ListVoucherRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voucherServiceClient) VoucherStats(ctx context.Context, in *VoucherStatsReq, opts ...grpc.CallOption) (*VoucherStatsReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VoucherStatsReply)
	err := c.cc.Invoke(ctx, VoucherService_VoucherStats_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VoucherServiceServer is the server API for VoucherService service.
// All implementations must embed UnimplementedVoucherServiceServer
// for forward compatibility.
type VoucherServiceServer interface {
	// 兑换记录列表
	ListVoucherRecord(context.Context, *ListVoucherRecordReq) (*ListVoucherRecordReply, error)
	// 兑换统计
	VoucherStats(context.Context, *VoucherStatsReq) (*VoucherStatsReply, error)
	mustEmbedUnimplementedVoucherServiceServer()
}

// UnimplementedVoucherServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedVoucherServiceServer struct{}

func (UnimplementedVoucherServiceServer) ListVoucherRecord(context.Context, *ListVoucherRecordReq) (*ListVoucherRecordReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListVoucherRecord not implemented")
}
func (UnimplementedVoucherServiceServer) VoucherStats(context.Context, *VoucherStatsReq) (*VoucherStatsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VoucherStats not implemented")
}
func (UnimplementedVoucherServiceServer) mustEmbedUnimplementedVoucherServiceServer() {}
func (UnimplementedVoucherServiceServer) testEmbeddedByValue()                        {}

// UnsafeVoucherServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VoucherServiceServer will
// result in compilation errors.
type UnsafeVoucherServiceServer interface {
	mustEmbedUnimplementedVoucherServiceServer()
}

func RegisterVoucherServiceServer(s grpc.ServiceRegistrar, srv VoucherServiceServer) {
	// If the following call pancis, it indicates UnimplementedVoucherServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&VoucherService_ServiceDesc, srv)
}

func _VoucherService_ListVoucherRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListVoucherRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherServiceServer).ListVoucherRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VoucherService_ListVoucherRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherServiceServer).ListVoucherRecord(ctx, req.(*ListVoucherRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VoucherService_VoucherStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VoucherStatsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoucherServiceServer).VoucherStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VoucherService_VoucherStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoucherServiceServer).VoucherStats(ctx, req.(*VoucherStatsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// VoucherService_ServiceDesc is the grpc.ServiceDesc for VoucherService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var VoucherService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.walletadmin.v1.VoucherService",
	HandlerType: (*VoucherServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListVoucherRecord",
			Handler:    _VoucherService_ListVoucherRecord_Handler,
		},
		{
			MethodName: "VoucherStats",
			Handler:    _VoucherService_VoucherStats_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/walletadmin/v1/voucher.proto",
}
