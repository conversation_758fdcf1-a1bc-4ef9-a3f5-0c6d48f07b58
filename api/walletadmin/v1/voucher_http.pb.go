// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: api/walletadmin/v1/voucher.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationVoucherServiceListVoucherRecord = "/api.walletadmin.v1.VoucherService/ListVoucherRecord"
const OperationVoucherServiceVoucherStats = "/api.walletadmin.v1.VoucherService/VoucherStats"

type VoucherServiceHTTPServer interface {
	// ListVoucherRecord 兑换记录列表
	ListVoucherRecord(context.Context, *ListVoucherRecordReq) (*ListVoucherRecordReply, error)
	// VoucherStats 兑换统计
	VoucherStats(context.Context, *VoucherStatsReq) (*VoucherStatsReply, error)
}

func RegisterVoucherServiceHTTPServer(s *http.Server, srv VoucherServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/voucher/record", _VoucherService_ListVoucherRecord0_HTTP_Handler(srv))
	r.GET("/admin/v1/voucher/stats", _VoucherService_VoucherStats0_HTTP_Handler(srv))
}

func _VoucherService_ListVoucherRecord0_HTTP_Handler(srv VoucherServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListVoucherRecordReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVoucherServiceListVoucherRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListVoucherRecord(ctx, req.(*ListVoucherRecordReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListVoucherRecordReply)
		return ctx.Result(200, reply)
	}
}

func _VoucherService_VoucherStats0_HTTP_Handler(srv VoucherServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in VoucherStatsReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationVoucherServiceVoucherStats)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.VoucherStats(ctx, req.(*VoucherStatsReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*VoucherStatsReply)
		return ctx.Result(200, reply)
	}
}

type VoucherServiceHTTPClient interface {
	ListVoucherRecord(ctx context.Context, req *ListVoucherRecordReq, opts ...http.CallOption) (rsp *ListVoucherRecordReply, err error)
	VoucherStats(ctx context.Context, req *VoucherStatsReq, opts ...http.CallOption) (rsp *VoucherStatsReply, err error)
}

type VoucherServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewVoucherServiceHTTPClient(client *http.Client) VoucherServiceHTTPClient {
	return &VoucherServiceHTTPClientImpl{client}
}

func (c *VoucherServiceHTTPClientImpl) ListVoucherRecord(ctx context.Context, in *ListVoucherRecordReq, opts ...http.CallOption) (*ListVoucherRecordReply, error) {
	var out ListVoucherRecordReply
	pattern := "/admin/v1/voucher/record"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationVoucherServiceListVoucherRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *VoucherServiceHTTPClientImpl) VoucherStats(ctx context.Context, in *VoucherStatsReq, opts ...http.CallOption) (*VoucherStatsReply, error) {
	var out VoucherStatsReply
	pattern := "/admin/v1/voucher/stats"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationVoucherServiceVoucherStats))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
