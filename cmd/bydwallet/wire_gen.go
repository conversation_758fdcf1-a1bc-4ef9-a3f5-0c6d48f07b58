// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/chain/evm/erc20"
	"byd_wallet/internal/biz/dapp"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/internal/biz/gaspool/paymaster"
	"byd_wallet/internal/biz/gaspool/paymaster/evm"
	"byd_wallet/internal/biz/gaspool/paymaster/tron"
	"byd_wallet/internal/biz/rent"
	tron2 "byd_wallet/internal/biz/syncer/chain/tron"
	"byd_wallet/internal/conf"
	"byd_wallet/internal/data"
	"byd_wallet/internal/data/bijie"
	"byd_wallet/internal/data/covalenthq"
	"byd_wallet/internal/data/metapath"
	"byd_wallet/internal/data/tronify"
	"byd_wallet/internal/data/weidubot"
	"byd_wallet/internal/server"
	"byd_wallet/internal/service"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, logger log.Logger) (*kratos.App, func(), error) {
	db, cleanup, err := data.NewGormDB(confData)
	if err != nil {
		return nil, nil, err
	}
	universalClient, cleanup2, err := data.NewRedisClient(confData)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	dataData := data.NewData(db, universalClient)
	userRepo := data.NewUserRepo(dataData)
	eventPublisher, cleanup3, err := data.NewEventPublisher(confData)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	userUsecase := biz.NewUserUsecase(logger, db, universalClient, userRepo, eventPublisher)
	gasPoolRepo := data.NewGasPoolRepo(dataData)
	userLocker := data.NewGasPoolUserLocker(dataData)
	spotPriceRepo, err := data.NewSpotPriceRepo(logger, dataData)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	hotAccountReader := data.NewGasPoolHotAccountReader(dataData)
	rpcEndpointRepo := data.NewRPCEndpointRepo(dataData)
	multiChainClient, cleanup4, err := data.NewEvmChainClient(rpcEndpointRepo)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	megafuelPaymaster, err := data.NewMegaFuelPaymaster(dataData)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	repoFactory := data.NewEvmPaymasterRepoFactory(dataData)
	gasPoolStatsService := data.NewGasPoolStatsService(universalClient)
	paymasterBuilder := evm.NewPaymasterBuilder(logger, spotPriceRepo, hotAccountReader, multiChainClient, megafuelPaymaster, repoFactory, gasPoolRepo, gasPoolRepo, gasPoolStatsService)
	smartNodeSelectionClient, cleanup5, err := data.NewSolanaChainClient(rpcEndpointRepo)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	solanaATAOwnerRepo := data.NewSolanaATAOwnerRepoProvider(dataData, logger)
	solanaPaymaster := data.NewSolanaPaymasterWithRedis(logger, spotPriceRepo, hotAccountReader, smartNodeSelectionClient, gasPoolRepo, gasPoolRepo, gasPoolRepo, universalClient, solanaATAOwnerRepo, gasPoolStatsService)
	roundRobinClient, cleanup6, err := data.NewTronChainClient(rpcEndpointRepo)
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	tronBandwidthPayWallet, err := data.NewTronBandwidthPayWallet(dataData, roundRobinClient)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	client, err := data.NewWeidubotClient(logger, dataData)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	tronRentApi := data.NewTronRentApi(dataData, client)
	tronAddressChecker := data.NewTronAddressChecker(dataData, roundRobinClient)
	asyncTaskMgr := data.NewTronAsyncTaskMgr(dataData)
	tronPaymaster := tron.NewPaymaster(logger, spotPriceRepo, gasPoolRepo, gasPoolRepo, roundRobinClient, tronBandwidthPayWallet, tronRentApi, tronAddressChecker, asyncTaskMgr, gasPoolStatsService)
	paymasterFactory := paymaster.NewPaymasterFactory(paymasterBuilder, solanaPaymaster, tronPaymaster)
	usecase := gaspool.NewUsecase(logger, gasPoolRepo, userLocker, paymasterFactory)
	userService := service.NewUserService(logger, userUsecase, usecase)
	walletUsecase := biz.NewWalletUsecase(logger, db)
	okxClient, err := data.NewOKXClient(dataData, logger)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	okxChainIndexDto := data.NewOKXChainIndexDto()
	tokenAssetRepo := data.NewTokenAssetRepo(logger, dataData, okxClient, okxChainIndexDto)
	tokenAssetUsecase := biz.NewTokenAssetUsecase(logger, tokenAssetRepo)
	blockchainNetworkRepo := data.NewBlockchainNetworkRepo(dataData)
	blockchainNetworkUsecase := biz.NewBlockchainNetworkUsecase(logger, blockchainNetworkRepo)
	userHoldTokenRepo := data.NewUserHoldTokenRepo(dataData)
	userHoldTokenUsecase := biz.NewUserHoldTokenUsecase(userHoldTokenRepo, logger)
	transactionRepo := data.NewTransactionRepo(dataData)
	transactionUsecase := biz.NewTransactionUsecase(logger, transactionRepo)
	walletSrvService := service.NewWalletSrvService(walletUsecase, tokenAssetUsecase, blockchainNetworkUsecase, userHoldTokenUsecase, transactionUsecase, usecase)
	tronRentRepo := data.NewTronRentRepo(dataData)
	tronRentRequester := tronify.NewTronRentClient(logger)
	weidubotTronRentRequester, err := weidubot.NewTronRentRequester(logger, client, roundRobinClient, db, universalClient)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	tronRentRequesterFactory := data.NewTronRentRequesterFactory(tronRentRequester, weidubotTronRentRequester)
	tronRentUseCase := rent.NewTronRentUseCase(tronRentRepo, tronRentRequesterFactory, logger)
	tronService := service.NewTronService(tronRentUseCase)
	coinDataThirdAPI, err := data.NewCoinDataThirdAPI(dataData)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	marketRepo := data.NewMarketRepo(dataData, logger, coinDataThirdAPI)
	marketUsecase := biz.NewMarketUsecase(logger, marketRepo)
	marketService := service.NewMarketService(marketUsecase)
	repo := data.NewDappRepo(dataData, okxClient, okxChainIndexDto)
	dbTx := data.NewDBTx(dataData)
	s3Repo, err := data.NewS3Repo(dataData)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	dappUsecase := dapp.NewUsecase(repo, dbTx, s3Repo, logger)
	approvalRepo := data.NewApprovalRepo(dataData)
	covalenthqClient, err := data.NewCovalenthqClient(dataData, logger)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	approvalFetcher := covalenthq.NewApprovalFetcher(covalenthqClient)
	approvalUsecase := biz.NewApprovalUsecase(approvalRepo, blockchainNetworkRepo, approvalFetcher)
	dappService := service.NewDappService(dappUsecase, approvalUsecase)
	swapRepo := data.NewSwapRepo(dataData)
	config, err := data.NewMetapathConfig(dataData)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	metapathClient := metapath.NewClient(config, logger)
	tokenSwapper := metapath.NewTokenSwapper(metapathClient, roundRobinClient, logger)
	swapTokenFetcher := metapath.NewSwapTokenFetcher(metapathClient, logger)
	tokenContractRepo := data.NewTokenContractRepo(logger, multiChainClient, roundRobinClient, smartNodeSelectionClient)
	tokenCollector := biz.NewTokenCollector(tokenAssetRepo, tokenContractRepo, s3Repo)
	transactionFetcher := tron2.NewTransactionFetcher(roundRobinClient)
	bizTransactionFetcher := biz.NewTransactionFetcher(transactionFetcher)
	swapUsecase := biz.NewSwapUsecase(logger, swapRepo, tokenSwapper, tokenAssetRepo, transactionRepo, swapTokenFetcher, tokenCollector, coinDataThirdAPI, blockchainNetworkRepo, bizTransactionFetcher)
	swapService := service.NewSwapService(swapUsecase)
	spotPriceManager := biz.NewSpotPriceManager(logger, spotPriceRepo)
	gasPoolService := service.NewGasPoolService(usecase, userUsecase, hotAccountReader, spotPriceManager, gasPoolStatsService)
	appVersionRepo := data.NewAppVersionRepo(dataData)
	appVersionUsecase := biz.NewAppVersionUsecase(appVersionRepo)
	appVersionService := service.NewAppVersionService(appVersionUsecase)
	userGuideRepo := data.NewUserGuideRepo(dataData)
	userGuideUsecase := biz.NewUserGuideUsecase(userGuideRepo)
	userGuideService := service.NewUserGuideService(userGuideUsecase)
	voucherRecordRepo := data.NewVoucherRecordRepo(dataData)
	userAddressRepo := data.NewUserAddressRepo(dataData)
	bijieConfig, err := data.NewBijieConfig(dataData)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	bijieClient := bijie.NewClient(logger, bijieConfig)
	voucherRepo, err := data.NewVoucherRepo(bijieClient, bijieConfig, tokenAssetRepo)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	voucherConfigRepo := data.NewVoucherConfigRepo(dataData)
	keyInfoRepo := data.NewKeyInfoRepo(dataData)
	erc20ERC20 := erc20.NewERC20()
	transactor := erc20.NewTransactor(multiChainClient, erc20ERC20)
	bizTransactor := biz.NewTransactor(keyInfoRepo, transactor)
	voucherUsecase := biz.NewVoucherUsecase(logger, voucherRecordRepo, userAddressRepo, voucherRepo, voucherConfigRepo, eventPublisher, bizTransactor, transactionRepo, blockchainNetworkRepo)
	voucherService := service.NewVoucherService(voucherUsecase)
	appRPCEndpointRepo := data.NewAppRPCEndpointRepo(dataData)
	appConfigUsecase := biz.NewAppConfigUsecase(appRPCEndpointRepo)
	appConfigService := service.NewAppConfigService(appConfigUsecase)
	httpServer := server.NewHTTPServer(confServer, userService, walletSrvService, tronService, marketService, dappService, swapService, gasPoolService, appVersionService, userGuideService, voucherService, appConfigService, logger)
	app := newApp(logger, httpServer)
	return app, func() {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
