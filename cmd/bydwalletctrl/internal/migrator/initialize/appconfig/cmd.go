package appconfig

import (
	"byd_wallet/common"
	"byd_wallet/common/constant"
	"byd_wallet/internal/data"
	"byd_wallet/model"
	"context"
	"github.com/urfave/cli/v3"
)

var Cmd = &cli.Command{
	Name:  "appconfig",
	Usage: "app配置数据初始化",
	Flags: []cli.Flag{
		&cli.StringFlag{
			Name:     "conf",
			Usage:    "数据库配置文件路径",
			Required: true,
		},
	},
	Action: run,
}

func run(ctx context.Context, cmd *cli.Command) error {
	bc, err := common.NewBootstrapConfig(cmd.String("conf"))
	if err != nil {
		return err
	}
	db, cleanup, err := data.NewGormDB(bc.Data)
	if err != nil {
		return err
	}
	defer cleanup()
	var proxyCount int64
	if err = db.WithContext(ctx).Model(&model.AppRPCEndpoint{}).Count(&proxyCount).Error; err != nil {
		return err
	}
	if proxyCount == 0 {
		proxiedEndpoints := []*model.AppRPCEndpoint{
			{
				ChainIndex: constant.BtcChainIndex,
				URL:        "https://still-soft-patina.btc.quiknode.pro/83d9eda3f3e272d94c4bf15b8e8dd51c439f607e/",
			},
			{
				ChainIndex: constant.EthChainIndex,
				URL:        "https://fabled-broken-tree.quiknode.pro/e28e0636e5aaacf7c0bd8ad9b47a507728dcbd99/",
			},
			{
				ChainIndex: constant.BscChainIndex,
				URL:        "https://white-compatible-night.bsc.quiknode.pro/20a0ee522a9ca2a4e56fe404b10b7e5b418cdb03/",
			},
			{
				ChainIndex: constant.PolChainIndex,
				URL:        "https://practical-fabled-smoke.matic.quiknode.pro/4ab2add088071533ed1da7082ea255b565f69af6/",
			},
			{
				ChainIndex: constant.OptimismChainIndex,
				URL:        "https://quick-ancient-mound.optimism.quiknode.pro/e301964a568d1b6fb639e4c05865ff25953a71b4/",
			},
			{
				ChainIndex: constant.ArbChainIndex,
				URL:        "https://green-green-cloud.arbitrum-mainnet.quiknode.pro/3d39ea85c212d48aa8a79a0b3fa72feb0f7a892e/",
			},
			{
				ChainIndex: constant.TronChainIndex,
				URL:        "https://cool-proportionate-wind.tron-mainnet.quiknode.pro/cea0ad09c07a84b7071806a79651deafba758175/",
			},
			{
				ChainIndex: constant.SolChainIndex,
				URL:        "https://bold-damp-ensemble.solana-mainnet.quiknode.pro/1c8794bfad096e455ce87d17b00dc942af2f88ea/",
			},
			{
				ChainIndex: constant.BaseChainIndex,
				URL:        "https://thrumming-evocative-surf.base-mainnet.quiknode.pro/3ea71d9095c718e3c5d95b058a1e4b1c6cc29202/",
			},
		}
		if err = db.WithContext(ctx).Create(&proxiedEndpoints).Error; err != nil {
			return err
		}
	}

	return nil
}
