package voucher

import (
	"byd_wallet/common"
	"byd_wallet/common/constant"
	"byd_wallet/internal/data"
	"byd_wallet/internal/data/bijie"
	"byd_wallet/model"
	"context"
	"encoding/json"
	"github.com/urfave/cli/v3"
)

var Cmd = &cli.Command{
	Name:  "voucher",
	Usage: "兑换券数据初始化",
	Flags: []cli.Flag{
		&cli.StringFlag{
			Name:     "conf",
			Usage:    "数据库配置文件路径",
			Required: true,
		},
		&cli.StringFlag{
			Name:     "address",
			Usage:    "平台地址",
			Required: true,
		},
	},
	Action: run,
}

func run(ctx context.Context, cmd *cli.Command) error {
	bc, err := common.NewBootstrapConfig(cmd.String("conf"))
	if err != nil {
		return err
	}
	db, cleanup, err := data.NewGormDB(bc.Data)
	if err != nil {
		return err
	}
	defer cleanup()

	api := "bijie"
	chainIndex := constant.BscChainIndex
	bjConf := &bijie.Config{
		BaseURL:      "https://api.528btc.com.cn",
		Debug:        true,
		ChainIndex:   chainIndex,
		TokenAddress: "******************************************",
	}
	confB, err := json.Marshal(bjConf)
	if err != nil {
		return err
	}
	if err = db.WithContext(ctx).FirstOrCreate(&model.APIConfig{
		API:    api,
		Config: confB,
	}, "api=?", api).Error; err != nil {
		return err
	}

	if err = db.WithContext(ctx).FirstOrCreate(&model.VoucherConfig{
		ChainIndex:      chainIndex,
		PlatformAddress: cmd.String("address"),
	}, "chain_index=?", chainIndex).Error; err != nil {
		return err
	}
	return nil
}
