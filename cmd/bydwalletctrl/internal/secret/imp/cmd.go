package imp

import (
	"byd_wallet/common"
	"byd_wallet/common/constant"
	"context"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/urfave/cli/v3"
	"os"
	"strings"
)

var Cmd = &cli.Command{
	Name:  "imp",
	Usage: "导入私钥",
	Flags: []cli.Flag{
		&cli.StringFlag{
			Name:     "conf",
			Usage:    "数据库配置文件路径",
			Required: true,
		},
		&cli.StringFlag{
			Name:     "file",
			Usage:    "私钥文件路径",
			Required: true,
		},
		&cli.Int64Flag{
			Name:  "chain",
			Usage: "链索引",
		},
	},
	Action: run,
}

func run(ctx context.Context, cmd *cli.Command) error {
	bc, err := common.NewBootstrapConfig(cmd.String("conf"))
	if err != nil {
		return err
	}
	logger := log.DefaultLogger
	uc, cf, err := wireUsecase(bc.Data, logger)
	if err != nil {
		return err
	}
	defer cf()
	file := cmd.String("file")
	chainIndex := cmd.Int64("chain")
	if !constant.IsValidChainIndex(chainIndex) {
		return errors.New("chain index is not valid")
	}
	key, err := os.ReadFile(file)
	if err != nil {
		return err
	}
	ki, err := uc.ImportPrivateKey(ctx, chainIndex, strings.TrimSpace(string(key)))
	if err != nil {
		return err
	}
	logger.Log(log.LevelInfo, "address", ki.Address)
	return nil
}
