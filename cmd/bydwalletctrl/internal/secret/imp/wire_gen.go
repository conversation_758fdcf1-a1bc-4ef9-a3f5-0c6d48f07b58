// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package imp

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/keyunpacker"
	"byd_wallet/internal/conf"
	"byd_wallet/internal/data"
	"github.com/go-kratos/kratos/v2/log"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireUsecase(confData *conf.Data, logger log.Logger) (*biz.KeyInfoUsecase, func(), error) {
	evm := keyunpacker.NewEVM()
	db, cleanup, err := data.NewGormDB(confData)
	if err != nil {
		return nil, nil, err
	}
	universalClient, cleanup2, err := data.NewRedisClient(confData)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	dataData := data.NewData(db, universalClient)
	keyInfoRepo := data.NewKeyInfoRepo(dataData)
	keyInfoUsecase := biz.NewKeyInfoUsecase(evm, keyInfoRepo)
	return keyInfoUsecase, func() {
		cleanup2()
		cleanup()
	}, nil
}
