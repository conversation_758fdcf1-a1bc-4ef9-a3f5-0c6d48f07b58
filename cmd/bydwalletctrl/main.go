package main

import (
	"byd_wallet/cmd/bydwalletctrl/internal/migrator"
	"byd_wallet/cmd/bydwalletctrl/internal/repair"
	"byd_wallet/cmd/bydwalletctrl/internal/secret"
	"byd_wallet/cmd/bydwalletctrl/test"
	"context"
	"log"
	"os"

	"github.com/urfave/cli/v3"
)

func main() {
	app := &cli.Command{
		Name:    "bydwalletctrl",
		Usage:   "bydwallet 命令行工具",
		Version: release,
		Commands: []*cli.Command{
			migrator.Cmd,
			test.Cmd,
			repair.Cmd,
			secret.Cmd,
		},
	}

	if err := app.Run(context.Background(), os.Args); err != nil {
		log.Fatal(err)
		return
	}
}
