// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/gaspool/paymaster/evm"
	"byd_wallet/internal/biz/gaspool/paymaster/tron"
	"byd_wallet/internal/conf"
	"byd_wallet/internal/data"
	"byd_wallet/internal/server"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confData *conf.Data, logger log.Logger) (*kratos.App, func(), error) {
	db, cleanup, err := data.NewGormDB(confData)
	if err != nil {
		return nil, nil, err
	}
	universalClient, cleanup2, err := data.NewRedisClient(confData)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	dataData := data.NewData(db, universalClient)
	spotPriceRepo, err := data.NewSpotPriceRepo(logger, dataData)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	gasPoolRepo := data.NewGasPoolRepo(dataData)
	rpcEndpointRepo := data.NewRPCEndpointRepo(dataData)
	roundRobinClient, cleanup3, err := data.NewTronChainClient(rpcEndpointRepo)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	tronBandwidthPayWallet, err := data.NewTronBandwidthPayWallet(dataData, roundRobinClient)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	client, err := data.NewWeidubotClient(logger, dataData)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	tronRentApi := data.NewTronRentApi(dataData, client)
	tronAddressChecker := data.NewTronAddressChecker(dataData, roundRobinClient)
	asyncTaskMgr := data.NewTronAsyncTaskMgr(dataData)
	gasPoolStatsService := data.NewGasPoolStatsService(universalClient)
	paymaster := tron.NewPaymaster(logger, spotPriceRepo, gasPoolRepo, gasPoolRepo, roundRobinClient, tronBandwidthPayWallet, tronRentApi, tronAddressChecker, asyncTaskMgr, gasPoolStatsService)
	hotAccountReader := data.NewGasPoolHotAccountReader(dataData)
	multiChainClient, cleanup4, err := data.NewEvmChainClient(rpcEndpointRepo)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	megafuelPaymaster, err := data.NewMegaFuelPaymaster(dataData)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	repoFactory := data.NewEvmPaymasterRepoFactory(dataData)
	paymasterBuilder := evm.NewPaymasterBuilder(logger, spotPriceRepo, hotAccountReader, multiChainClient, megafuelPaymaster, repoFactory, gasPoolRepo, gasPoolRepo, gasPoolStatsService)
	evmPaymasters := biz.NewEVMPaymastersForAsync(paymasterBuilder)
	gasPoolServer := server.NewGasPoolServer(logger, paymaster, evmPaymasters)
	app := newApp(logger, gasPoolServer)
	return app, func() {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
