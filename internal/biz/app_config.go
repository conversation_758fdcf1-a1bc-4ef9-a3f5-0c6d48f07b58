package biz

import (
	"byd_wallet/common/constant"
	"byd_wallet/model"
	"byd_wallet/utils"
	"context"
	"encoding/json"
)

type AppRPCEndpointRepo interface {
	ListAppRPCEndpoint(ctx context.Context) ([]*model.AppRPCEndpoint, error)
}

type AppConfig struct {
	EncryptedEndpoints string
}
type AppConfigUsecase struct {
	rpcRepo AppRPCEndpointRepo
	aesKey  string
}

type AppRPCEndpoint struct {
	ChainID    string `json:"chain_id"`
	ChainIndex int64  `json:"chain_index"`
	Url        string `json:"url"`
}

func NewAppConfigUsecase(rpcRepo AppRPCEndpointRepo) *AppConfigUsecase {
	return &AppConfigUsecase{
		rpcRepo: rpcRepo,
		aesKey:  "aqW07LykZ6TCceW1vfq07ws9XQKjbfNH",
	}
}

func (c *AppConfigUsecase) GetAppConfig(ctx context.Context) (*AppConfig, error) {
	endpoints, err := c.GetEncodedRPCEndpoints(ctx)
	if err != nil {
		return nil, err
	}
	return &AppConfig{
		EncryptedEndpoints: endpoints,
	}, nil
}

func (c *AppConfigUsecase) GetEncodedRPCEndpoints(ctx context.Context) (string, error) {
	endpoints, err := c.rpcRepo.ListAppRPCEndpoint(ctx)
	if err != nil {
		return "", err
	}
	list := make([]*AppRPCEndpoint, len(endpoints))
	for i, endpoint := range endpoints {
		list[i] = &AppRPCEndpoint{
			ChainID:    constant.ChainIndex2ChainID[endpoint.ChainIndex],
			ChainIndex: endpoint.ChainIndex,
			Url:        endpoint.URL,
		}
	}
	data, err := json.Marshal(&list)
	if err != nil {
		return "", err
	}
	return utils.EncryptWithAESCBC(data, []byte(c.aesKey))
}
