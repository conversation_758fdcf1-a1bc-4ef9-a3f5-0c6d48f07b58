package erc20

import (
	"byd_wallet/common/constant"
	evmCrypto "byd_wallet/internal/biz/chain/evm"
	"byd_wallet/internal/biz/syncer/chain/evm"
	"byd_wallet/model"
	"context"
	"fmt"
	"github.com/ethereum/go-ethereum/accounts/abi/bind/v2"
	"github.com/ethereum/go-ethereum/common"
	"math/big"
)

type Transactor struct {
	cli   *evm.MultiChainClient
	erc20 *ERC20
}

func NewTransactor(cli *evm.MultiChainClient, erc20 *ERC20) *Transactor {
	return &Transactor{cli: cli, erc20: erc20}
}

func (t *Transactor) Transfer(ctx context.Context, chainIndex int64, privateKey, tokenAddress, to, value string, dryrun ...bool) (*model.Transaction, error) {
	client, err := t.cli.Select(chainIndex)
	if err != nil {
		return nil, err
	}
	priv, err := evmCrypto.HexToECDSA(privateKey)
	if err != nil {
		return nil, err
	}
	tokenAddr := common.HexToAddress(tokenAddress)
	toAddr := common.HexToAddress(to)
	amount, ok := new(big.Int).SetString(value, 10)
	if !ok {
		return nil, fmt.Errorf("invalid value: %s", value)
	}
	chainID, err := client.ChainID(ctx)
	if err != nil {
		return nil, err
	}
	bc := t.erc20.Instance(client, tokenAddr)
	calldata := t.erc20.PackTransfer(toAddr, amount)
	txOps := bind.NewKeyedTransactor(priv, chainID)
	txOps.Context = ctx
	gasPrice, err := client.SuggestGasPrice(ctx)
	if err != nil {
		return nil, err
	}
	txOps.GasPrice = gasPrice
	if len(dryrun) > 0 && dryrun[0] {
		txOps.NoSend = true
	}
	tx, err := bc.RawTransact(txOps, calldata)
	if err != nil {
		return nil, err
	}
	return &model.Transaction{
		TxHash:       tx.Hash().Hex(),
		BlockNumber:  0,
		ChainIndex:   chainIndex,
		FromAddress:  txOps.From.Hex(),
		ToAddress:    to,
		Value:        value,
		Fee:          "",
		Method:       constant.TransferMethod,
		ProgramID:    tokenAddress,
		Status:       constant.TransactionStatusPending,
		Timestamp:    0,
		TokenDecimal: 0,
	}, nil
}
