package dapp

import (
	"byd_wallet/internal/biz/base"
	"byd_wallet/internal/biz/dbtx"
	"byd_wallet/model"
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

type AdminUsecase struct {
	repo AdminRepo
	tx   dbtx.DBTx
	s3   base.S3Repo
	log  *log.Helper
}

func NewAdminUsecase(repo AdminRepo, tx dbtx.DBTx, s3 base.S3Repo, logger log.Logger) *AdminUsecase {
	return &AdminUsecase{repo: repo, tx: tx, s3: s3, log: log.NewHelper(logger)}
}

func (uc AdminUsecase) CreateDapp(ctx context.Context, dapp *model.Dapp) error {
	dapp.ID = 0
	dapp.CreatedAt = time.Time{}
	dapp.Logo = uc.s3.ToAppAccessUrl(ctx, dapp.Logo)
	return uc.repo.CreateDapp(ctx, dapp)
}

func (uc AdminUsecase) DeleteDapp(ctx context.Context, id uint) error {
	return uc.repo.DeleteDapp(ctx, id)
}

func (uc AdminUsecase) UpdateDapp(ctx context.Context, dapp *model.Dapp) error {
	dapp.Logo = uc.s3.ToAppAccessUrl(ctx, dapp.Logo)
	return uc.tx.ExecTx(ctx, func(ctx context.Context) error {
		oldDapp, err := uc.repo.GetDapp(ctx, dapp.ID)
		if err != nil {
			return err
		}
		dapp.CreatedAt = oldDapp.CreatedAt
		dapp.UpdatedAt = time.Now()
		if err := uc.repo.UpdateDapp(ctx, dapp); err != nil {
			return err
		}
		return nil
	})
}

func (uc AdminUsecase) ListDapp(ctx context.Context) ([]*model.Dapp, error) {
	return uc.repo.ListDapp(ctx)
}
