package evm

import (
	"byd_wallet/model"
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum/common"
)

func (pm *Paymaster) handleWaitGasTask(ctx context.Context, gasTransferWaitSecs int64) {
	ids, err := pm.repo.AllWaitGasTask(ctx)
	if err != nil {
		pm.log.Errorf("AllWaitGasTask: %v", err)
		return
	}
	if len(ids) == 0 {
		return
	}
	pm.log.Infof("handleWaitGasTask: count=%d", len(ids))

	g := pm.waitGasTaskPool.NewGroup()

	nowUnix := time.Now().Unix()
	for _, id := range ids {
		g.Submit(func() {
			stx, err := pm.stxMgr.FindGasPoolSponsorTxByID(ctx, id)
			if err != nil {
				pm.log.Errorf("get sponsor tx error: %v", err)
				terr := pm.repo.RemoveWaitGasTask(ctx, id)
				if terr != nil {
					pm.log.Errorf("RemoveWaitGasTask by db fail: %v: id=%d", terr, id)
				}
				return
			}

			// 检查任务是否过期（参考Tron paymaster的过期机制）
			spent := nowUnix - stx.CreatedAt.Unix()

			if stx.Status != model.GasPoolTxStatusWaitGas {
				err := pm.repo.RemoveWaitGasTask(ctx, id)
				if err != nil {
					pm.log.Errorf("RemoveWaitGasTask by useless tx: %v: id=%d", err, id)
				}
				return
			}
			confirmed, err := pm.checkGasTransferConfirmed(ctx, stx.ChainIndex, stx.NativeTxHash)
			if !confirmed {
				pm.log.Debugf("%s链gas转账尚未确认，继续等待，交易哈希: %s",
					pm.chainName, stx.TxHash)
				return
			}
			update := &model.GasPoolSponsorTx{}
			update.ID = stx.ID
			txHash, err := pm.sendTransactionByRawTx(ctx, stx.RawTxHex)
			if err != nil {
				// 区分不同类型的错误进行处理
				if strings.Contains(err.Error(), "nonce too low") {
					// nonce too low 错误通常是不可恢复的，直接标记为失败
					pm.log.Warnf("%s链交易nonce过低，标记为失败: %v, txID=%d", pm.chainName, err, id)
					reErr := pm.repo.RemoveWaitGasTask(ctx, id)
					if reErr != nil {
						pm.log.Errorf("RemoveWaitGasTask by nonce too low: %v: id=%d", reErr, id)
					}
					update.Status = model.GasPoolTxStatusFail
					update.Reason = err.Error()
					if err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update); err != nil {
						pm.log.Errorf("UpdateGasPoolSponsorTx by nonce too low: %v: update=%+v", err, update)
					}
					return
				} else if strings.Contains(err.Error(), "insufficient funds") {
					// insufficient funds 错误可能是临时的，检查是否应该继续等待
					pm.log.Warnf("%s链余额不足，检查是否继续等待: %v, txID=%d, 已等待%d秒",
						pm.chainName, err, id, spent)

					// 如果已经等待超过一定时间（比如总等待时间的80%），则标记为失败
					maxWaitForFunds := gasTransferWaitSecs * 4 / 5 // 80%的等待时间
					if spent >= maxWaitForFunds {
						pm.log.Errorf("%s链余额不足且等待时间过长，标记为失败: txID=%d, 已等待%d秒",
							pm.chainName, id, spent)
						reErr := pm.repo.RemoveWaitGasTask(ctx, id)
						if reErr != nil {
							pm.log.Errorf("RemoveWaitGasTask by insufficient funds timeout: %v: id=%d", reErr, id)
						}
						update.Status = model.GasPoolTxStatusFail
						update.Reason = fmt.Sprintf("insufficient funds after waiting %d seconds: %s", spent, err.Error())
						if err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update); err != nil {
							pm.log.Errorf("UpdateGasPoolSponsorTx by insufficient funds timeout: %v: update=%+v", err, update)
						}
						return
					}

					// 否则继续等待，不删除任务
					pm.log.Infof("%s链余额不足但继续等待，txID=%d, 已等待%d秒，最大等待%d秒",
						pm.chainName, id, spent, maxWaitForFunds)
					return
				}

				// 其他错误类型，记录日志但继续等待
				pm.log.Errorf("%s链发送交易失败，继续等待: %v, txID=%d", pm.chainName, err, id)
				return
			}
			pm.log.Debugf("%s链交易发送成功，交易哈希: %s", pm.chainName, txHash)

			update.Status = model.GasPoolTxStatusPending
			if err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update); err != nil {
				pm.log.Errorf("UpdateGasPoolSponsorTx by wait deposit tx: %v: update=%+v", err, update)
				return
			}

			err = pm.repo.AddWaitDepositTxTask(ctx, id)
			if err != nil {
				pm.log.Errorf("AddWaitDepositTxTask: %v: txID=%d", err, update.ID)
			}
			return

		})
	}
	_ = g.Wait()
}
func (pm *Paymaster) handleWaitUserSendTxTask(ctx context.Context, gasTransferWaitSecs int64) {
	ids, err := pm.repo.AllWaitDepositTxTask(ctx)
	if err != nil {
		pm.log.Errorf("AllDepositTask: %v", err)
		return
	}
	if len(ids) == 0 {
		return
	}
	pm.log.Infof("handleWaitDepositTask: count=%d", len(ids))

	g := pm.waitDepositTxTaskPool.NewGroup()

	//nowUnix := time.Now().Unix()
	for _, id := range ids {
		g.Submit(func() {
			stx, err := pm.stxMgr.FindGasPoolSponsorTxByID(ctx, id)
			if err != nil {
				pm.log.Errorf("get sponsor tx error: %v", err)
				terr := pm.repo.RemoveWaitDepositTxTask(ctx, id)
				if terr != nil {
					pm.log.Errorf("RemoveWaitGasTask by db fail: %v: id=%d", terr, id)
				}
				return
			}

			if stx.Status != model.GasPoolTxStatusPending {
				err := pm.repo.RemoveWaitDepositTxTask(ctx, id)
				if err != nil {
					pm.log.Errorf("RemoveWaitGasTask by useless tx: %v: id=%d", err, id)
				}
				return
			}
			update := &model.GasPoolSponsorTx{}
			update.ID = stx.ID
			confirmed, err := pm.checkGasTransferConfirmed(ctx, stx.ChainIndex, stx.TxHash)
			if !confirmed && err != nil {
				update.Status = model.GasPoolTxStatusFail
				pm.log.Debugf("%s链gas转账尚未确认，继续等待，交易哈希: %s",
					pm.chainName, stx.TxHash)
				err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
				return
			}
			if !confirmed {
				return
			}
			update.Status = model.GasPoolTxStatusSuccess

			if stx.TxType.IsUseGasPool() {
				if recordErr := pm.statsService.RecordGasUsage(ctx, stx.UserID, stx.GasUSDT); recordErr != nil {
					pm.log.Errorf("%s链记录gas pool使用统计失败: %v，用户ID: %d，消耗金额: %s",
						pm.chainName, recordErr, stx.UserID, stx.GasUSDT.String())
					// 统计记录失败不影响主流程，继续执行
				}
			}
			// wait deposit tx
			if stx.TxType.IsDepositGasPool() {
				// 执行gas pool充值
				depositFlow, err := pm.gpMgr.DepositGasPool(ctx, stx.UserID, stx.ValueUSDT, stx.ChainIndex, stx.TxHash)
				if err != nil {
					pm.log.Errorf("%s链执行gas pool充值失败: %v，交易ID: %d",
						pm.chainName, err, stx.ID)
					return
				}
				update.DepositFlowID = depositFlow.ID

			}

			err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
			if err != nil {
				pm.log.Errorf("UpdateGasPoolSponsorTx by finished: %v: txID=%d, status=%s", err, update.ID, update.Status)
			}
		})
	}
	_ = g.Wait()
}

// checkGasTransferConfirmed 检查gas转账交易是否已确认
func (pm *Paymaster) checkGasTransferConfirmed(ctx context.Context, chainIndex int64, txHash string) (bool, error) {
	// 获取对应链的EVM客户端
	client, err := pm.evmCli.Select(chainIndex)
	if err != nil {
		return false, fmt.Errorf("failed to get EVM client for chain %s: %w", pm.chainName, err)
	}

	// 获取交易收据以检查确认状态
	receipt, err := client.TransactionReceipt(ctx, common.HexToHash(txHash))
	if err != nil {
		// 如果交易还未被打包，返回false但不报错
		// 这是正常情况，需要继续等待
		pm.log.Debugf("%s链gas转账交易尚未被打包，交易哈希: %s", pm.chainName, txHash)
		return false, nil
	}

	// 检查交易执行状态（1表示成功，0表示失败）
	if receipt.Status == 1 {
		pm.log.Debugf("%s链gas转账交易确认成功，交易哈希: %s，区块号: %d",
			pm.chainName, txHash, receipt.BlockNumber.Uint64())
		return true, nil
	} else {
		// 交易已确认但执行失败
		pm.log.Errorf("%s链gas转账交易执行失败，交易哈希: %s，区块号: %d",
			pm.chainName, txHash, receipt.BlockNumber.Uint64())
		return false, fmt.Errorf("gas transfer transaction execution failed")
	}
}
