package evm

import (
	"byd_wallet/common/constant"
	"context"
	"math/big"
)

func (pm *Paymaster) GetL1DataFee(rawTxHex string) *big.Int {
	l1DataFee := big.NewInt(0)
	if pm.chainIndex == constant.OptimismChainIndex || pm.chainIndex == constant.BaseChainIndex {
		l1DataFee, err := pm.estimateOptimismL1DataFee(context.Background(), rawTxHex)
		if err != nil {
			pm.log.Warnf("估算Optimism L1 data fee失败，使用默认值: %v", err)
			l1DataFee = pm.getDefaultOptimismL1DataFee()
		}
		pm.log.Debugf("Optimism L1 data fee估算: %s wei", l1DataFee.String())
		return l1DataFee
	} else {
		l1DataFee = big.NewInt(0) // 其他EVM链无L1 data fee
	}
	return l1DataFee
}
