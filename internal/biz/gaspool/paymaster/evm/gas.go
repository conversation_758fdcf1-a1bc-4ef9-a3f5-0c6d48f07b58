package evm

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/utils"
	"context"
	"fmt"
	"math/big"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/shopspring/decimal"
)

func (pm *Paymaster) estimateGas(ctx context.Context, tx *gaspool.UserTx) (*gaspool.UserTxGas, error) {
	// 输入验证
	if tx == nil {
		return nil, fmt.Errorf("chainIndex:%v invalid  tx", pm.chainIndex)
	}

	pm.log.Debugf("开始估算%s链交易gas费用（包含用户交易和gas转账），交易哈希: %s", pm.chainName, tx.TxHash)

	evmTx, err := utils.RlpDecodeBytes(tx.RawTxHex)
	if err != nil {
		return nil, fmt.Errorf("raw error: %w", err)
	}

	if evmTx.Gas() == 0 {
		return pm.bscEstimateGas(ctx, tx, evmTx)
	}
	return pm.evmEstimateGas(ctx, tx, evmTx)
}

func (pm *Paymaster) evmEstimateGas(ctx context.Context, tx *gaspool.UserTx, evmTx *types.Transaction) (*gaspool.UserTxGas, error) {
	// 步骤1: 计算用户原始交易的gas费用
	userGasLimit := evmTx.Gas()
	userGasPrice := evmTx.GasPrice()

	pm.log.Debugf("BSC链检测到零gas价格交易，将使用MegaFuel服务，无需gas转账")
	userGasWei := new(big.Int).Mul(big.NewInt(int64(userGasLimit)), userGasPrice)

	nativeTxFee := decimal.Zero
	nativeTxValue := decimal.Zero
	nativeTxGas := decimal.Zero

	pm.log.Debugf("用户交易gas估算 - GasLimit: %d, GasPrice: %s wei, 总费用: %s wei",
		userGasLimit, userGasPrice.String(), userGasWei.String())

	// 步骤2: 估算gas转账交易的费用
	gasTransferFee, err := pm.estimateGasTransferFee(ctx, evmTx)
	if err != nil {
		pm.log.Warnf("估算gas转账费用失败，使用默认值: %v", err)
		// 使用默认的gas转账费用作为后备方案
		gasTransferFee = pm.getDefaultGasTransferFee(userGasPrice)
	}

	pm.log.Debugf("gas转账交易费用估算: %s wei", gasTransferFee.String())

	// 步骤3: 计算L1 data fee（仅适用于Optimism链）
	l1DataFee := pm.GetL1DataFee(tx.RawTxHex)
	pm.log.Debugf("l1DataFee = %v", l1DataFee.String())
	// 步骤4: 计算总gas费用（用户交易 + gas转账 + L1 data fee）
	totalGasWei := new(big.Int).Add(userGasWei, gasTransferFee)

	// 加上
	totalGasWei.Add(totalGasWei, l1DataFee)
	// 加上转账的l1Fee
	totalGasWei.Add(totalGasWei, l1DataFee)
	totalGasDecimal := decimal.NewFromBigInt(totalGasWei, 0)

	nativeTxFee = nativeTxFee.Add(decimal.NewFromBigInt(gasTransferFee, 0))
	nativeTxValue = nativeTxValue.Add(decimal.NewFromBigInt(userGasWei, 0))
	nativeTxGas = nativeTxGas.Add(decimal.NewFromBigInt(gasTransferFee, 0)).Add(decimal.NewFromBigInt(l1DataFee, 0))

	// 步骤4: 获取原生代币价格
	price, timeUnix, err := pm.tokenPriceReader.GetTokenLatestPriceUSDT(ctx, pm.chainIndex, "")
	if err != nil {
		return nil, fmt.Errorf("get price error: %w", err)
	}
	// 检查价格是否过期
	if pm.isPriceTimeExpired(timeUnix) {
		return nil, fmt.Errorf("price expired: %d", timeUnix)
	}

	// 步骤5: 计算USDT价值
	// gas费用计算考虑链特定的USDT精度差异
	// 注意：gas pool系统统一使用6位精度USDT进行计算，与链上USDT代币精度无关
	baseUnit := pm.getBaseUnit()
	gasInNativeToken := totalGasDecimal.Div(baseUnit)
	gasUSDT := gasInNativeToken.Mul(price).Mul(decimalsUSDT) // decimalsUSDT = 10^6 (gas pool标准精度)
	// calculate value usdt
	if tx.Contract == "" {
		tx.ValueUSDT = gasInNativeToken.Mul(price).Mul(decimalsUSDT)
	} else {
		tx.ValueUSDT = tx.Value
	}
	// 记录链特定的gas费用计算信息
	chainName := constant.GetChainName(pm.chainIndex)
	pm.log.Debugf("%s链gas费用计算 - 原生代币数量: %s, 价格: %s USDT, userFee:%v gas费用: %s USDT (gas pool 6位精度)",
		chainName, gasInNativeToken.String(), price.String(), totalGasWei.String(), gasUSDT.String())

	pm.log.Debugf("%s链gas估算完成 - 用户交易: %s wei, gas转账: %s wei, 总计: %s wei, Price: %s USDT, GasUSDT: %s",
		pm.chainName, userGasWei.String(), gasTransferFee.String(), totalGasDecimal.String(), price.String(), gasUSDT.String())
	gas := &gaspool.UserTxGas{
		Gas:           totalGasDecimal,
		GasUSDT:       gasUSDT,
		Price:         price,
		PriceTimeUnix: timeUnix,
		NativeTxFee:   nativeTxFee,
		NativeTxGas:   nativeTxGas,
		NativeTxValue: nativeTxValue,
	}
	return gas, nil
}

func (pm *Paymaster) bscEstimateGas(ctx context.Context, tx *gaspool.UserTx, evmTx *types.Transaction) (*gaspool.UserTxGas, error) {
	if !pm.isBscChain() {
		return nil, fmt.Errorf("bsc chain unavailable")
	}
	// 步骤1: 计算用户原始交易的gas费用
	userGasLimit := evmTx.Gas()
	userGasPrice := evmTx.GasPrice()
	if userGasPrice.Sign() == 0 {
		client, err := pm.evmCli.Select(pm.chainIndex)
		if err != nil {
			return nil, err
		}
		// 如果用户交易没有设置gas价格，获取当前网络建议价格用于计算
		userGasPrice, err = client.SuggestGasPrice(ctx)
		if err != nil {
			pm.log.Warnf("获取建议gas价格失败，使用默认值: %v", err)
			userGasPrice = big.NewInt(constant.DefaultBaseFee + constant.DefaultGasTipCap)
		}
	}
	userGasWei := new(big.Int).Mul(big.NewInt(int64(userGasLimit)), userGasPrice)

	nativeTxFee := decimal.Zero
	nativeTxValue := decimal.Zero
	nativeTxGas := decimal.Zero

	pm.log.Debugf("用户交易gas估算 - GasLimit: %d, GasPrice: %s wei, 总费用: %s wei",
		userGasLimit, userGasPrice.String(), userGasWei.String())

	// 步骤4: 计算总gas费用（用户交易 + gas转账 + L1 data fee）
	totalGasWei := userGasWei
	totalGasDecimal := decimal.NewFromBigInt(totalGasWei, 0)

	// 步骤4: 获取原生代币价格
	price, timeUnix, err := pm.tokenPriceReader.GetTokenLatestPriceUSDT(ctx, pm.chainIndex, "")
	if err != nil {
		return nil, fmt.Errorf("get price error: %w", err)
	}
	// 检查价格是否过期
	if pm.isPriceTimeExpired(timeUnix) {
		return nil, fmt.Errorf("price expired: %d", timeUnix)
	}

	// 步骤5: 计算USDT价值
	// gas费用计算考虑链特定的USDT精度差异
	// 注意：gas pool系统统一使用6位精度USDT进行计算，与链上USDT代币精度无关
	baseUnit := pm.getBaseUnit()
	gasInNativeToken := totalGasDecimal.Div(baseUnit)
	gasUSDT := gasInNativeToken.Mul(price).Mul(decimalsUSDT) // decimalsUSDT = 10^6 (gas pool标准精度)
	// calculate value usdt
	if tx.Contract == "" {
		tx.ValueUSDT = gasInNativeToken.Mul(price).Mul(decimalsUSDT)
	} else {
		tx.ValueUSDT = tx.Value
	}
	// 记录链特定的gas费用计算信息
	chainName := constant.GetChainName(pm.chainIndex)
	pm.log.Debugf("%s链gas费用计算 - 原生代币数量: %s, 价格: %s USDT, userFee:%v gas费用: %s USDT (gas pool 6位精度)",
		chainName, gasInNativeToken.String(), price.String(), totalGasWei.String(), gasUSDT.String())

	gas := &gaspool.UserTxGas{
		Gas:           totalGasDecimal,
		GasUSDT:       gasUSDT,
		Price:         price,
		PriceTimeUnix: timeUnix,
		NativeTxFee:   nativeTxFee,
		NativeTxGas:   nativeTxGas,
		NativeTxValue: nativeTxValue,
	}
	return gas, nil
}

func (pm *Paymaster) estimateGasTransferFee(ctx context.Context, userTx *types.Transaction) (*big.Int, error) {
	pm.log.Debugf("开始估算%s链gas转账交易费用", pm.chainName)

	// 步骤1: 获取交易发送者地址
	senderAddr, err := utils.GetTxSender(userTx)
	if err != nil {
		pm.log.Warnf("获取交易发送者地址失败，使用默认gas转账费用: %v", err)
		return pm.getDefaultGasTransferFee(userTx.GasPrice()), nil
	}

	// 步骤2: 获取EVM客户端（关键步骤，失败时立即回退）
	client, err := pm.evmCli.Select(pm.chainIndex)
	if err != nil {
		pm.log.Warnf("无法获取%s链EVM客户端（可能在测试环境），回退到默认gas转账费用计算: %v", pm.chainName, err)
		return pm.getDefaultGasTransferFee(userTx.GasPrice()), nil
	}

	// 步骤3: 获取热钱包私钥和地址
	hotWalletPrivateKey, err := pm.hotAccountReader.GetHotAccount(ctx, pm.chainIndex)
	if err != nil {
		pm.log.Warnf("获取热钱包私钥失败，使用默认gas转账费用: %v", err)
		return pm.getDefaultGasTransferFee(userTx.GasPrice()), nil
	}

	hotWalletAddr, _, err := utils.GetAddressByPrivateKey(hotWalletPrivateKey)
	if err != nil {
		pm.log.Warnf("解析热钱包地址失败，使用默认gas转账费用: %v", err)
		return pm.getDefaultGasTransferFee(userTx.GasPrice()), nil
	}

	// 步骤4: 获取当前gas价格
	// 为了与gas转账交易的传统格式保持一致，这里也使用传统的gas价格获取方式
	suggestedGasPrice, err := client.SuggestGasPrice(ctx)
	if err != nil {
		pm.log.Warnf("获取建议gas价格失败，使用用户交易的gas价格: %v", err)
		suggestedGasPrice = userTx.GasPrice()
		if suggestedGasPrice == nil || suggestedGasPrice.Sign() <= 0 {
			pm.log.Warnf("用户交易gas价格无效，回退到默认gas转账费用计算")
			return pm.getDefaultGasTransferFee(userTx.GasPrice()), nil
		}
	}

	// 应用1.2x倍数策略以确保gas转账交易能够快速确认
	gasPriceMultiplier := decimal.NewFromFloat(DefaultGasLimitMultiplier)
	adjustedGasPrice := decimal.NewFromBigInt(suggestedGasPrice, 0).Mul(gasPriceMultiplier).BigInt()

	// 步骤5: 创建模拟的gas转账交易用于估算
	// 使用较小的转账金额进行估算（1 wei），但保持与实际交易相同的格式
	simulationValue := big.NewInt(1)

	// 步骤6: 使用EstimateGas进行精确估算
	// 使用传统交易格式进行估算，确保与实际发送的交易格式一致
	estimatedGasLimit, err := client.EstimateGas(ctx, ethereum.CallMsg{
		From:     hotWalletAddr,
		To:       &senderAddr,
		GasPrice: adjustedGasPrice, // 使用传统gas价格字段而非EIP-1559字段
		Value:    simulationValue,
		Data:     nil,
	})
	if err != nil {
		pm.log.Warnf("动态gas估算失败，回退到默认gas转账费用计算: %v", err)
		return pm.getDefaultGasTransferFee(userTx.GasPrice()), nil
	}

	// 步骤7: 应用安全系数
	// 使用链特定的gas倍数，不同链可能需要不同的安全系数
	safetyFactor := pm.getChainSpecificGasMultiplier()
	safeGasLimit := decimal.NewFromInt(int64(estimatedGasLimit)).Mul(safetyFactor)
	finalGasLimit := safeGasLimit.BigInt()

	// 步骤8: 计算总gas费用
	// 使用调整后的gas价格计算最终费用
	totalGasTransferFee := new(big.Int).Mul(finalGasLimit, adjustedGasPrice)

	pm.log.Debugf("动态gas转账费用估算成功 - 估算GasLimit: %d, 安全GasLimit: %s, GasPrice: %s wei, 总费用: %s wei",
		estimatedGasLimit, finalGasLimit.String(), adjustedGasPrice.String(), totalGasTransferFee.String())

	return totalGasTransferFee, nil
}

// getDefaultGasTransferFee 获取默认的gas转账费用（作为后备方案）
// 参数:
//   - userGasPrice: 用户交易的gas价格，用作参考
//
// 返回值:
//   - *big.Int: 默认的gas转账费用（以wei为单位）
func (pm *Paymaster) getDefaultGasTransferFee(userGasPrice *big.Int) *big.Int {
	// 使用标准ETH转账的gas limit (21000) 乘以安全系数 (1.2)
	defaultGasLimit := big.NewInt(21000)
	safetyFactor := decimal.NewFromFloat(1.2)
	safeGasLimit := decimal.NewFromBigInt(defaultGasLimit, 0).Mul(safetyFactor)

	// 使用用户交易的gas价格或默认值
	gasPrice := userGasPrice
	if gasPrice == nil || gasPrice.Sign() <= 0 {
		gasPrice = big.NewInt(constant.DefaultBaseFee + constant.DefaultGasTipCap)
	}

	// 计算默认gas转账费用
	defaultFee := new(big.Int).Mul(safeGasLimit.BigInt(), gasPrice)

	pm.log.Debugf("使用默认gas转账费用 - GasLimit: %s, GasPrice: %s wei, 总费用: %s wei",
		safeGasLimit.String(), gasPrice.String(), defaultFee.String())

	return defaultFee
}

// getBaseUnit 获取链的基本单位
// 返回值:
//   - decimal.Decimal: 链的基本单位
func (pm *Paymaster) getBaseUnit() decimal.Decimal {
	switch pm.chainIndex {
	case constant.EthChainIndex, constant.ArbChainIndex, constant.OptimismChainIndex, constant.BaseChainIndex:
		return constant.BaseUnitPerETH
	case constant.BscChainIndex:
		return constant.BaseUnitPerBNB // BSC链BNB使用18位精度，与ETH相同
	case constant.PolChainIndex:
		return constant.BaseUnitPerETH // Polygon也使用18位精度
	default:
		return constant.BaseUnitPerETH // 默认使用ETH单位
	}
}

// getChainSpecificGasMultiplier 获取链特定的gas费用倍数
// 不同链可能需要不同的安全系数
func (pm *Paymaster) getChainSpecificGasMultiplier() decimal.Decimal {
	switch pm.chainIndex {
	case constant.OptimismChainIndex:
		// Optimism由于L1 data fee的存在，需要稍高的安全系数
		return decimal.NewFromFloat(1.3)
	case constant.BscChainIndex:
		// BSC链使用标准系数，与原BSC paymaster保持一致
		return decimal.NewFromFloat(DefaultGasLimitMultiplier)
	case constant.ArbChainIndex:
		// Arbitrum通常gas费用较低，使用标准系数
		return decimal.NewFromFloat(DefaultGasLimitMultiplier)
	case constant.BaseChainIndex:
		// Base链使用标准系数
		return decimal.NewFromFloat(DefaultGasLimitMultiplier)
	case constant.EthChainIndex:
		// 以太坊主网使用标准系数
		return decimal.NewFromFloat(DefaultGasLimitMultiplier)
	case constant.PolChainIndex:
		// Polygon使用标准系数
		return decimal.NewFromFloat(DefaultGasLimitMultiplier)
	default:
		// 默认使用标准系数
		return decimal.NewFromFloat(DefaultGasLimitMultiplier)
	}
}
