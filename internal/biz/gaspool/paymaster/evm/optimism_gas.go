package evm

import (
	"byd_wallet/common/constant"
	"context"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"

	"github.com/ethereum/go-ethereum/core/types"
	"github.com/shopspring/decimal"
)

// optimism_gas.go - Optimism链特有的gas费用计算逻辑
// 处理L1 data fee的估算和计算

const (
	// DefaultOptimismL1DataFee 默认的Optimism L1 data fee（当oracle不可用时）
	DefaultOptimismL1DataFee = 50000000000000 // 0.00005 ETH in wei

	// OptimismL1DataFeeMultiplier L1 data fee安全系数
	OptimismL1DataFeeMultiplier = 1.3
)

// estimateOptimismL1DataFee 估算Optimism L1 data fee
// 参数:
//   - ctx: 上下文对象
//   - rawTxHex: 原始交易的十六进制字符串
//
// 返回值:
//   - *big.Int: L1 data fee（以wei为单位）
//   - error: 错误信息
func (pm *Paymaster) estimateOptimismL1DataFee(ctx context.Context, rawTxHex string) (*big.Int, error) {
	pm.log.Debugf("开始估算Optimism L1 data fee，交易数据长度: %d", len(rawTxHex))

	// 步骤1: 解码交易数据
	txData, err := hex.DecodeString(strings.TrimPrefix(rawTxHex, "0x"))
	if err != nil {
		return nil, fmt.Errorf("failed to decode transaction data: %w", err)
	}

	return pm.calculateL1DataFeeFromContract(ctx, txData)
}

// getDefaultOptimismL1DataFee 获取默认的Optimism L1 data fee
// 当oracle不可用或计算失败时使用
func (pm *Paymaster) getDefaultOptimismL1DataFee() *big.Int {
	defaultFee := big.NewInt(DefaultOptimismL1DataFee)
	pm.log.Debugf("使用默认Optimism L1 data fee: %s wei", defaultFee.String())
	return defaultFee
}

// isOptimismChain 检查是否为Optimism链
func (pm *Paymaster) isOptimismChain() bool {
	return pm.chainIndex == constant.OptimismChainIndex
}

// estimateOptimismGasTransferFee 估算Optimism链的gas转账费用
// 考虑L1 data fee的影响
func (pm *Paymaster) estimateOptimismGasTransferFee(ctx context.Context, userTx *types.Transaction) (*big.Int, error) {
	// 对于Optimism，gas转账本身也会产生L1 data fee
	// 但为了简化，我们使用标准的EVM gas转账费用估算
	// L1 data fee在主要的EstimateGas方法中单独计算

	return pm.estimateGasTransferFee(ctx, userTx)
}

// calculateOptimismTotalGasFee 计算Optimism链的总gas费用
// 包括L2 execution fee和L1 data fee
func (pm *Paymaster) calculateOptimismTotalGasFee(ctx context.Context, userGasWei, gasTransferFee, l1DataFee *big.Int) *big.Int {
	// 总费用 = L2 execution fee (用户交易 + gas转账) + L1 data fee
	l2ExecutionFee := new(big.Int).Add(userGasWei, gasTransferFee)
	totalFee := new(big.Int).Add(l2ExecutionFee, l1DataFee)

	pm.log.Debugf("Optimism总gas费用计算 - L2执行费用: %s wei, L1数据费用: %s wei, 总费用: %s wei",
		l2ExecutionFee.String(), l1DataFee.String(), totalFee.String())

	return totalFee
}

// validateOptimismTransaction 验证Optimism交易的特殊要求
// 确保交易符合Optimism网络的规范
func (pm *Paymaster) validateOptimismTransaction(ctx context.Context, rawTxHex string) error {
	// 基本验证：检查交易数据格式
	if !strings.HasPrefix(rawTxHex, "0x") {
		return fmt.Errorf("transaction data must start with 0x")
	}

	// 解码验证
	_, err := hex.DecodeString(strings.TrimPrefix(rawTxHex, "0x"))
	if err != nil {
		return fmt.Errorf("invalid transaction data format: %w", err)
	}

	// 长度验证：防止过大的交易
	if len(rawTxHex) > 131072 { // 64KB limit
		return fmt.Errorf("transaction data too large, length: %d", len(rawTxHex))
	}

	pm.log.Debugf("Optimism交易验证通过，数据长度: %d", len(rawTxHex))
	return nil
}

// getOptimismChainSpecificMultiplier 获取Optimism链特定的费用倍数
// 用于调整gas费用估算的准确性
func (pm *Paymaster) getOptimismChainSpecificMultiplier() decimal.Decimal {
	// Optimism由于L1 data fee的存在，可能需要更高的安全系数
	return decimal.NewFromFloat(1.3) // 比标准EVM链稍高的安全系数
}

// logOptimismGasBreakdown 记录Optimism gas费用的详细分解
// 用于调试和监控
func (pm *Paymaster) logOptimismGasBreakdown(userGasWei, gasTransferFee, l1DataFee, totalGasWei *big.Int) {
	userGasDecimal := decimal.NewFromBigInt(userGasWei, 0)
	gasTransferDecimal := decimal.NewFromBigInt(gasTransferFee, 0)
	l1DataDecimal := decimal.NewFromBigInt(l1DataFee, 0)
	totalDecimal := decimal.NewFromBigInt(totalGasWei, 0)

	// 计算各部分占比
	userPercent := userGasDecimal.Div(totalDecimal).Mul(decimal.NewFromInt(100))
	transferPercent := gasTransferDecimal.Div(totalDecimal).Mul(decimal.NewFromInt(100))
	l1Percent := l1DataDecimal.Div(totalDecimal).Mul(decimal.NewFromInt(100))

	pm.log.Infof("Optimism gas费用分解 - 用户交易: %s wei (%.2f%%), gas转账: %s wei (%.2f%%), L1数据: %s wei (%.2f%%), 总计: %s wei",
		userGasWei.String(), userPercent,
		gasTransferFee.String(), transferPercent,
		l1DataFee.String(), l1Percent,
		totalGasWei.String())
}

// handleOptimismSpecialCases 处理Optimism的特殊情况
// 例如存款交易、系统交易等
func (pm *Paymaster) handleOptimismSpecialCases(ctx context.Context, rawTxHex string) (bool, error) {
	// 检查是否为Optimism存款交易（type 0x7E）
	// 存款交易通常不需要用户支付L1 data fee

	txData, err := hex.DecodeString(strings.TrimPrefix(rawTxHex, "0x"))
	if err != nil {
		return false, fmt.Errorf("failed to decode transaction data: %w", err)
	}

	// 简单检查：如果交易数据很小，可能是特殊交易
	if len(txData) < 10 {
		pm.log.Debugf("检测到可能的Optimism特殊交易，数据长度: %d", len(txData))
		return true, nil
	}

	return false, nil
}

// calculateL1DataFeeFromContract 直接通过合约调用计算L1 data fee
// 当参数获取失败或不可靠时使用此方法作为后备方案
// 参数:
//   - ctx: 上下文对象
//   - txData: 交易数据（RLP编码后的字节）
//
// 返回值:
//   - *big.Int: L1 data fee（以wei为单位）
//   - error: 错误信息
func (pm *Paymaster) calculateL1DataFeeFromContract(ctx context.Context, txData []byte) (*big.Int, error) {
	// 直接调用合约的getL1Fee方法
	contractFee, err := pm.optimismOracle.GetL1FeeFromContract(ctx, txData)
	if err != nil {
		pm.log.Warnf("合约调用失败，使用默认L1 data fee: %v", err)
		return pm.getDefaultOptimismL1DataFee(), nil
	}

	// 应用安全系数
	safetyFactor := decimal.NewFromFloat(OptimismL1DataFeeMultiplier)
	contractFeeDecimal := decimal.NewFromBigInt(contractFee, 0)
	adjustedL1DataFee := contractFeeDecimal.Mul(safetyFactor)
	finalL1DataFee := adjustedL1DataFee.BigInt()

	pm.log.Debugf("合约调用L1 data fee成功 - 原始费用: %s wei, 安全系数: %.2f, 最终费用: %s wei",
		contractFee.String(), OptimismL1DataFeeMultiplier, finalL1DataFee.String())

	return finalL1DataFee, nil
}
