package evm

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/internal/biz/gaspool/base"
	"byd_wallet/internal/biz/syncer/chain/evm"
	"byd_wallet/internal/thirdapi/megafuel"
	"byd_wallet/model"
	"byd_wallet/utils"
	"context"
	"fmt"
	"math/big"
	"time"

	"github.com/alitto/pond/v2"

	"github.com/ethereum/go-ethereum/common"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
)

// paymaster.go - EVM paymaster 主文件
// 包含 paymaster 结构体定义、构造函数和核心接口实现
// 支持 Ethereum、Polygon、Arbitrum、Optimism、Base 等主流 EVM 兼容链

const (
	// DefaultPriceExpireSeconds 默认价格过期时间（秒）
	DefaultPriceExpireSeconds = 300 // 5分钟
	// DefaultGasLimitMultiplier 默认gas limit倍数
	DefaultGasLimitMultiplier = 1.2
	// DefaultGasTransferWaitSeconds 默认gas转账等待确认时间（秒）
	DefaultGasTransferWaitSeconds = 180   // 180秒
	DefaultGasLimit               = 26000 // 2秒

)

var decimalsUSDT = decimal.NewFromInt(1000000)

// Repo EVM paymaster数据访问接口
type Repo interface {
	AddWaitGasTask(ctx context.Context, txID uint) error
	AllWaitGasTask(ctx context.Context) ([]uint, error)
	RemoveWaitGasTask(ctx context.Context, txID uint) error

	AddWaitDepositTxTask(ctx context.Context, txID uint) error
	AllWaitDepositTxTask(ctx context.Context) ([]uint, error)
	RemoveWaitDepositTxTask(ctx context.Context, txID uint) error
}

// RepoFactory EVM paymaster repo工厂函数类型
// 用于为不同的链索引创建独立的repo实例
type RepoFactory func(chainIndex int64) Repo

// PaymasterBuilder EVM paymaster构建器
// 支持为每个EVM链创建独立的repo实例，避免不同链之间的数据混淆
type PaymasterBuilder struct {
	log              *log.Helper
	tokenPriceReader base.TokenPriceReader
	hotAccountReader base.HotAccountReader
	evmCli           *evm.MultiChainClient
	// MegaFuel 客户端
	megafuelClient *megafuel.Paymaster
	repoFactory    RepoFactory // repo工厂函数，为每个链创建独立的repo
	stxMgr         base.GasPoolSponsorTxMgr
	gpMgr          base.GasPoolMgr
	statsService   base.GasPoolStatsService // gas pool统计服务
}

// NewPaymasterBuilder 创建新的EVM paymaster构建器
// 参数:
//   - logger: 日志记录器
//   - tokenPriceReader: 代币价格读取器
//   - hotAccountReader: 热钱包账户读取器
//   - evmCli: EVM多链客户端
//   - repoFactory: repo工厂函数，用于为每个链创建独立的repo实例
//   - stxMgr: gas pool sponsor交易管理器
//   - statsService: gas pool统计服务
func NewPaymasterBuilder(
	logger log.Logger,
	tokenPriceReader base.TokenPriceReader,
	hotAccountReader base.HotAccountReader,
	evmCli *evm.MultiChainClient,
	megafuelClient *megafuel.Paymaster,
	repoFactory RepoFactory,
	stxMgr base.GasPoolSponsorTxMgr,
	gpMgr base.GasPoolMgr,
	statsService base.GasPoolStatsService,
) *PaymasterBuilder {
	return &PaymasterBuilder{
		log:              log.NewHelper(logger),
		tokenPriceReader: tokenPriceReader,
		hotAccountReader: hotAccountReader,
		evmCli:           evmCli,
		megafuelClient:   megafuelClient,
		repoFactory:      repoFactory,
		stxMgr:           stxMgr,
		gpMgr:            gpMgr,
		statsService:     statsService,
	}
}

// Build 构建指定链的paymaster实例
// 为每个链创建独立的repo实例，确保不同链的数据隔离
func (b *PaymasterBuilder) Build(chainIndex int64) *Paymaster {
	chainName := constant.GetChainName(chainIndex)
	if chainName == "" {
		chainName = fmt.Sprintf("Chain_%d", chainIndex)
	}

	// 为当前链创建独立的repo实例
	chainRepo := b.repoFactory(chainIndex)

	// 创建基础paymaster实例
	pm := &Paymaster{
		log:                b.log,
		tokenPriceReader:   b.tokenPriceReader,
		hotAccountReader:   b.hotAccountReader,
		evmCli:             b.evmCli,
		repo:               chainRepo, // 使用链专用的repo实例
		stxMgr:             b.stxMgr,
		gpMgr:              b.gpMgr,
		statsService:       b.statsService, // gas pool统计服务
		chainIndex:         chainIndex,
		chainName:          chainName,
		priceExpireSeconds: DefaultPriceExpireSeconds,
		stopCh:             nil, // 在Start方法中初始化
	}

	// 为Optimism链初始化特有的oracle客户端
	if pm.IsOpL2(chainIndex) {
		client, err := b.evmCli.Select(chainIndex)
		if err != nil {
			b.log.Warnf("无法获取Optimism客户端，L1 data fee计算将使用默认值: %v", err)
		} else {
			optimismOracle, err := NewOptimismOracle(b.log.Logger(), client)
			if err != nil {
				b.log.Warnf("初始化Optimism oracle失败，L1 data fee计算将使用默认值: %v", err)
			} else {
				pm.optimismOracle = optimismOracle
				b.log.Infof("成功初始化Optimism oracle，支持精确的L1 data fee计算")
			}
		}

	}

	return pm
}

// Paymaster 统一的EVM paymaster实现
// 支持Ethereum、Polygon、Arbitrum、Optimism、Base等主流EVM兼容链
type Paymaster struct {
	log              *log.Helper
	tokenPriceReader base.TokenPriceReader
	hotAccountReader base.HotAccountReader
	evmCli           *evm.MultiChainClient
	megafuelClient   *megafuel.Paymaster
	repo             Repo
	stxMgr           base.GasPoolSponsorTxMgr
	gpMgr            base.GasPoolMgr
	statsService     base.GasPoolStatsService // gas pool统计服务
	// 链配置
	chainIndex int64
	chainName  string

	// 缓存字段，避免重复计算
	cachedAddress common.Address

	// 配置字段
	priceExpireSeconds int64 // 价格过期时间（秒）

	// Optimism特有组件（仅在Optimism链上使用）
	optimismOracle OptimismOracleInterface // Optimism gas price oracle客户端

	// 异步任务控制
	stopCh                chan struct{}
	waitGasTaskPool       pond.Pool
	waitDepositTxTaskPool pond.Pool
}

func (pm *Paymaster) DecodeUserTx(ctx context.Context, rawTxHex string, txType model.GasPoolTxType) (tx *gaspool.UserTx, err error) {
	return pm.decodeTransferTx(ctx, rawTxHex, txType)

}

func (pm *Paymaster) VerifyUserTxSignature(ctx context.Context, tx *gaspool.UserTx) (bool, error) {
	pm.log.Debugf("验证%s链用户交易签名，交易哈希: %s", pm.chainName, tx.TxHash)
	// 获取链ID
	chainID := constant.GetChainID(pm.chainIndex)
	if chainID == 0 {
		return false, fmt.Errorf("invalid chainIndex: %d", pm.chainIndex)
	}

	evmTx, err := utils.RlpDecodeBytes(tx.RawTxHex)
	if err != nil {
		return false, err
	}

	// 验证签名
	sender, err := utils.GetTxSender(evmTx)
	if err != nil {
		return false, fmt.Errorf("s: %w", err)
	}

	cli, err := pm.evmCli.Select(pm.chainIndex)
	if err != nil {
		return false, err
	}
	nonce, err := cli.NonceAt(ctx, sender, nil)
	if err != nil {
		return false, err
	}
	if nonce > evmTx.Nonce() {
		return false, fmt.Errorf("invalid noce: %v,chain nonce:%v", nonce, evmTx.Nonce())
	}

	// 有eth转账
	if evmTx.Value().Sign() > 0 {
		balance, err := cli.BalanceAt(ctx, sender, nil)
		if err != nil {
			return false, fmt.Errorf("get balance failed: %w", err)
		}
		if balance.Cmp(evmTx.Value()) < 0 {
			return false, fmt.Errorf("insufficient balance: tx=%v, balance=%v",
				evmTx.Value().String(), balance.String())
		}
	}

	pm.log.Debugf("%s链交易签名验证成功，发送者: %s", pm.chainName, sender.Hex())
	return true, nil

}

func (pm *Paymaster) EstimateGas(ctx context.Context, tx *gaspool.UserTx) (*gaspool.UserTxGas, error) {
	return pm.estimateGas(ctx, tx)
}

func (pm *Paymaster) SendDepositTx(ctx context.Context, tx *model.GasPoolSponsorTx) (err error) {
	// TODO 性能监控：记录开始时间
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		pm.log.Infof("%s链交易处理完成，耗时: %v", pm.chainName, duration)
	}()

	// 验证输入参数
	if tx == nil {
		return fmt.Errorf("tx is nil")
	}
	update := &model.GasPoolSponsorTx{}
	update.ID = tx.ID

	evmTx, err := utils.RlpDecodeBytes(tx.RawTxHex)
	if err != nil {
		return err
	}
	txHash, err := pm.sendTransaction(ctx, evmTx)
	if err != nil {
		return err
	}
	// 更新交易哈希
	tx.TxHash = txHash

	update.Status = model.GasPoolTxStatusPending
	//update.TxHash = txHash
	if terr := pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update); terr != nil {
		pm.log.Errorf("UpdateGasPoolSponsorTx by wait gas: %v: update=%+v", terr, update)
	}
	if err = pm.repo.AddWaitDepositTxTask(ctx, tx.ID); err != nil {
		pm.log.Errorf("创建gas转账等待确认记录失败: %v: %+v", err, tx.ID)
		return err
	}

	return
}

// SendSponsorTx 发送原始交易到EVM网络（异步确认模式）
// 注意：此方法实现异步确认模式，gas转账确认后才会发送用户交易
func (pm *Paymaster) SendSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) (err error) {
	// 性能监控：记录开始时间
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		pm.log.Infof("%s链交易处理完成，耗时: %v", pm.chainName, duration)
	}()

	// 验证输入参数
	if tx == nil {
		return fmt.Errorf("tx is nil")
	}

	evmTx, err := utils.RlpDecodeBytes(tx.RawTxHex)
	if err != nil {
		return err
	}

	if evmTx.GasPrice().Sign() == 0 && pm.isBscChain() {
		return pm.sendBscSponsorTx(ctx, tx)
	}
	return pm.sendEvmSponsorTx(ctx, tx)
}

func (pm *Paymaster) sendEvmSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) (err error) {
	// 性能监控：记录开始时间
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		pm.log.Infof("%s链交易处理完成，耗时: %v", pm.chainName, duration)
	}()

	// 验证输入参数
	if tx == nil {
		return fmt.Errorf("tx is nil")
	}

	evmTx, err := utils.RlpDecodeBytes(tx.RawTxHex)
	if err != nil {
		return err
	}

	// 获取交易发送者地址
	sender, err := utils.GetTxSender(evmTx)
	if err != nil {
		pm.log.Errorf("获取交易发送者地址失败，交易ID: %d, 错误: %v", tx.ID, err)
		return fmt.Errorf("failed to get transaction sender: %w", err)
	}

	pm.log.Debugf("交易准备完成，发送者地址: %s，开始处理gas费用转账", sender.Hex())
	update := &model.GasPoolSponsorTx{}
	update.ID = tx.ID

	gasPrice := evmTx.GasPrice()                    // *big.Int
	gasLimit := new(big.Int).SetUint64(evmTx.Gas()) // uint64 -> big.Int
	// 计算 fee (wei)
	feeWei := new(big.Int).Mul(gasPrice, gasLimit)
	l1DataFee := pm.GetL1DataFee(tx.RawTxHex)
	feeWei = new(big.Int).Add(feeWei, l1DataFee)
	// 转换成 decimal
	fee := decimal.NewFromBigInt(feeWei, 0)
	// 步骤1: 从热钱包向交易发送者转账gas费用
	gasTransferTxHash, err := pm.transferGasToSender(ctx, sender, fee)
	if err != nil {
		pm.log.Errorf("向交易发送者转账gas费用失败，交易ID: %d, 发送者: %s, 错误: %v",
			tx.ID, sender.Hex(), err)
		if !tx.TxType.IsPreReduceGasPool() {
			_, refundGasErr := pm.gpMgr.RefundGasPool(ctx, tx.ReduceFlowID, tx.Gas)
			if refundGasErr != nil {
				pm.log.Errorf("退款交易ID: %d, 发送者: %s, 错误: %v",
					tx.ID, sender.Hex(), err)
			}
		}

		return fmt.Errorf("failed to transfer gas to sender: %w", err)
	}

	update.NativeTxHash = gasTransferTxHash
	pm.log.Infof("成功向发送者 %s 转账gas费用，转账交易哈希: %s，等待确认后发送用户交易",
		sender.Hex(), gasTransferTxHash)

	update.Status = model.GasPoolTxStatusWaitGas
	//update.TxHash = txHash
	if terr := pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update); terr != nil {
		pm.log.Errorf("UpdateGasPoolSponsorTx by wait gas: %v: update=%+v", terr, update)
	}

	if err = pm.repo.AddWaitGasTask(ctx, tx.ID); err != nil {
		pm.log.Errorf("创建gas转账等待确认记录失败: %v: %+v", err, tx.ID)
	}

	pm.log.Infof("%s链交易进入异步确认模式，交易ID: %d，gas转账哈希: %s",
		pm.chainName, tx.ID, gasTransferTxHash)
	return nil
}

func (pm *Paymaster) sendBscSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) (err error) {
	// 性能监控：记录开始时间
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		pm.log.Infof("%s链交易处理完成，耗时: %v", pm.chainName, duration)
	}()
	evmTx, err := utils.RlpDecodeBytes(tx.RawTxHex)
	if err != nil {
		return err
	}
	// 构建MegaFuel交易参数
	transactionArgs, err := pm.buildMegaFuelTransactionArgs(evmTx)
	if err != nil {
		return err
	}

	// 调用MegaFuel服务（注意：当前MegaFuel客户端可能不支持上下文，这里先保留原有调用方式）
	txHash, err := pm.megafuelClient.SendTransaction(transactionArgs, tx.RawTxHex)
	// 验证输入参数
	if err != nil {
		return err
	}

	update := &model.GasPoolSponsorTx{}
	update.ID = tx.ID
	update.NativeTxHash = tx.TxHash
	update.TxHash = txHash
	update.Status = model.GasPoolTxStatusPending
	//update.TxHash = txHash
	if terr := pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update); terr != nil {
		pm.log.Errorf("UpdateGasPoolSponsorTx by wait gas: %v: update=%+v", terr, update)
	}

	if err = pm.repo.AddWaitDepositTxTask(ctx, tx.ID); err != nil {
		pm.log.Errorf("创建gas转账等待确认记录失败: %v: %+v", err, tx.ID)
	}

	return nil
}

// Start 启动EVM paymaster异步任务
// 参数:
//   - ctx: 上下文对象
//
// 返回值:
//   - error: 错误信息
func (pm *Paymaster) Start(ctx context.Context) error {
	if pm.stopCh == nil {
		pm.stopCh = make(chan struct{})
	}

	if pm.waitGasTaskPool == nil {
		pm.waitGasTaskPool = pond.NewPool(10)
	}
	if pm.waitDepositTxTaskPool == nil {
		pm.waitDepositTxTaskPool = pond.NewPool(10)
	}
	// 等待gas
	go func() {
		t := time.NewTicker(time.Second * 3)
		defer t.Stop()

		pm.log.Infof("启动%s链paymaster异步任务: gas转账确认", pm.chainName)

		var (
			gasTransferWaitSecs int64 = DefaultGasTransferWaitSeconds
		)

		for {
			select {
			case <-pm.stopCh:
				pm.log.Infof("停止%s链paymaster异步任务", pm.chainName)
				return
			case <-t.C:
				pm.handleWaitGasTask(ctx, gasTransferWaitSecs)
			}
		}
	}()

	go func() {
		t := time.NewTicker(time.Second * 3)
		defer t.Stop()

		pm.log.Infof("启动%s链paymaster异步任务: gas转账确认", pm.chainName)

		var (
			gasTransferWaitSecs int64 = DefaultGasTransferWaitSeconds
		)

		for {
			select {
			case <-pm.stopCh:
				pm.log.Infof("停止%s链paymaster异步任务", pm.chainName)
				return
			case <-t.C:
				pm.handleWaitUserSendTxTask(ctx, gasTransferWaitSecs)
			}
		}
	}()

	return nil
}

// Stop 停止EVM paymaster异步任务
// 参数:
//   - ctx: 上下文对象
//
// 返回值:
//   - error: 错误信息
func (pm *Paymaster) Stop(ctx context.Context) error {
	if pm.stopCh != nil {
		close(pm.stopCh)
		pm.log.Infof("%s链paymaster异步任务已停止", pm.chainName)
	}
	return nil
}
