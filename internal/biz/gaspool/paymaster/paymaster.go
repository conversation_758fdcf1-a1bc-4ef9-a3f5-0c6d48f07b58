package paymaster

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/internal/biz/gaspool/paymaster/evm"
	"byd_wallet/internal/biz/gaspool/paymaster/solana"
	"byd_wallet/internal/biz/gaspool/paymaster/tron"
	"context"
	"fmt"
)

type paymasterFactory struct {
	pms map[int64]gaspool.Paymaster
}

func NewPaymasterFactory(
	evmPay *evm.PaymasterBuilder,
	solanaPay *solana.Paymaster,
	tronPay *tron.Paymaster,
) gaspool.PaymasterFactory {
	return &paymasterFactory{
		pms: map[int64]gaspool.Paymaster{
			constant.SolChainIndex:  solanaPay,
			constant.TronChainIndex: tronPay,
			// evm - 统一的EVM paymaster架构支持所有标准EVM兼容链
			// BSC链已迁移到统一EVM架构，支持BSC特殊的代币精度处理和MegaFuel集成
			constant.BscChainIndex:      evmPay.Build(constant.BscChainIndex),
			constant.EthChainIndex:      evmPay.Build(constant.EthChainIndex),
			constant.BaseChainIndex:     evmPay.Build(constant.BaseChainIndex),
			constant.ArbChainIndex:      evmPay.Build(constant.ArbChainIndex),
			constant.PolChainIndex:      evmPay.Build(constant.PolChainIndex),
			constant.OptimismChainIndex: evmPay.Build(constant.OptimismChainIndex), // 添加Optimism L2链支持
		},
	}
}

func (p *paymasterFactory) GetPaymaster(ctx context.Context, chainIndex int64) (gaspool.Paymaster, error) {
	pm, ok := p.pms[chainIndex]
	if !ok {
		return nil, fmt.Errorf("paymaster not found for chain index %d", chainIndex)
	}
	return pm, nil
}
