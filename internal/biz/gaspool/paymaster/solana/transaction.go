package solana

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/gaspool"
	"context"
	"fmt"

	"github.com/shopspring/decimal"

	"github.com/gagliardetto/solana-go"
)

func (pm *Paymaster) decodeTransferTx(ctx context.Context, rawTx string) (*gaspool.UserTx, error) {
	var tx gaspool.UserTx

	// Step 1: 使用统一的交易准备函数
	transaction, err := prepareTransactionFromHex(rawTx)
	if err != nil {
		return nil, wrapError("prepare transaction for decoding", err)
	}

	// Step 2: 解析交易消息
	txInfos, err := pm.parseMessage(&transaction.Message)
	if err != nil {
		return nil, fmt.Errorf("parseMessage error: %v", err)
	}

	if len(transaction.Signatures) < 0 {
		return nil, fmt.Errorf("invalid transaction signatures")
	}
	fmt.Println("transaction signatures:", transaction.Signatures[0].String())
	if err := pm.signTransaction(transaction); err != nil {
		return nil, err
	}
	tx.TxHash = transaction.Signatures[0].String()

	// Step 3: 选择主要的转账信息
	// 对于包含多个指令的交易，我们需要智能选择主要的转账信息
	txInfo := pm.selectPrimaryTransferInfo(txInfos)

	tx.From = txInfo.From
	tx.To = txInfo.To
	tx.Value = decimal.NewFromInt(int64(txInfo.Value))
	tx.Contract = txInfo.Mint

	pm.log.Debugf("选择的主要转账信息: %s -> %s, 金额: %s, 合约: %s",
		tx.From, tx.To, tx.Value.String(), tx.Contract)

	// 判断是 txInfo.Mint
	price, timeUnix, err := pm.tokenPriceReader.GetTokenLatestPriceUSDT(ctx, constant.SolChainIndex, "")
	if err != nil {
		return nil, wrapError("get token price", err)
	}
	if pm.isPriceTimeExpired(timeUnix) {
		return nil, fmt.Errorf("token price expired at timestamp: %d", timeUnix)
	}
	scale := decimal.NewFromInt(10).Pow(pm.decimals(txInfo.Mint))

	// calculate value usdt
	if tx.Contract == "" {
		tx.ValueUSDT = tx.Value.Mul(price).Div(scale).Mul(decimalsUSDT)
	} else {
		tx.ValueUSDT = tx.Value
	}
	// Step 4: 获取系统充值接收地址
	depositReceiverAddress, err := pm.dAddrMgr.FindDepositReceiverAddress(ctx, constant.SolChainIndex)
	if err != nil {
		pm.log.Warnf("获取 Solana 链充值接收地址失败: %v，将跳过充值地址检查", err)
		return &tx, nil
	}

	// Step 5: 检查是否为系统充值地址或关联代币账户
	isSystemDeposit := false

	// 普通地址直接匹配
	if tx.To == depositReceiverAddress {
		pm.log.Infof("检测到充值交易：接收地址 %s 为系统充值地址", tx.To)
		isSystemDeposit = true
	}

	// SPL Token 情况下检查 ATA 地址
	if !isSystemDeposit && txInfo.Mint != "" {
		isATA, normalizedTo := pm.isAssociatedTokenAddress(tx.To, txInfo.Mint, depositReceiverAddress)
		if isATA {
			pm.log.Infof("检测到 SPL Token 充值交易：接收地址 %s 为系统充值地址的关联代币账户", tx.To)
			tx.To = normalizedTo
			isSystemDeposit = true
		}
	}

	if !isSystemDeposit {
		pm.log.Debugf("普通转账交易：接收地址 %s 不是系统充值地址（系统充值地址: %s）", tx.To, depositReceiverAddress)
	}

	pm.log.Debugf("成功解码转账交易，合约地址: %s，从 %s 到 %s，金额: %s", tx.Contract, tx.From, tx.To, tx.Value.String())
	return &tx, nil
}

func (pm *Paymaster) isAssociatedTokenAddress(
	toAddress string, mint string, depositReceiverAddress string,
) (bool, string) {
	depositPubkey, err := solana.PublicKeyFromBase58(depositReceiverAddress)
	if err != nil {
		pm.log.Warnf("无效的充值地址格式: %s, 错误: %v", depositReceiverAddress, err)
		return false, ""
	}

	mintPubkey, err := solana.PublicKeyFromBase58(mint)
	if err != nil {
		pm.log.Warnf("无效的代币 Mint 地址格式: %s, 错误: %v", mint, err)
		return false, ""
	}

	ata, _, err := solana.FindAssociatedTokenAddress(depositPubkey, mintPubkey)
	if err != nil {
		pm.log.Warnf("计算关联代币账户地址失败: %v", err)
		return false, ""
	}

	if toAddress == ata.String() {
		return true, depositReceiverAddress
	}
	return false, ""
}

// selectPrimaryTransferInfo 从多个交易信息中选择主要的转账信息
// 参数：
//   - txInfos: 解析出的交易信息列表
//
// 返回：
//   - *TransactionInfo: 选择的主要交易信息
func (pm *Paymaster) selectPrimaryTransferInfo(txInfos []*TransactionInfo) *TransactionInfo {
	if len(txInfos) == 0 {
		return &TransactionInfo{}
	}

	// 如果只有一个交易信息，直接返回
	if len(txInfos) == 1 {
		return txInfos[0]
	}

	pm.log.Debugf("检测到多个交易信息，开始选择主要转账信息")

	// 优先级规则：
	// 1. SPL Token 转账优先于 SOL 转账（因为通常 SOL 转账是为了支付租金）
	// 2. 金额较大的转账优先
	// 3. 如果都是相同类型，选择第一个

	var primaryTx *TransactionInfo
	var maxValue uint64 = 0
	var hasTokenTransfer bool

	for i, txInfo := range txInfos {
		pm.log.Debugf("交易信息 %d: 类型=%s, 从=%s, 到=%s, 金额=%d, 合约=%s",
			i, txInfo.Type, txInfo.From, txInfo.To, txInfo.Value, txInfo.Mint)

		// 检查是否为代币转账
		isTokenTransfer := txInfo.Mint != "" && txInfo.Type != constant.NativeTokenType

		// 选择逻辑
		if primaryTx == nil {
			// 第一个交易信息
			primaryTx = txInfo
			maxValue = txInfo.Value
			hasTokenTransfer = isTokenTransfer
		} else if !hasTokenTransfer && isTokenTransfer {
			// 当前主要交易是 SOL 转账，新的是代币转账，优先选择代币转账
			primaryTx = txInfo
			maxValue = txInfo.Value
			hasTokenTransfer = true
		} else if hasTokenTransfer == isTokenTransfer && txInfo.Value > maxValue {
			// 相同类型的转账，选择金额较大的
			primaryTx = txInfo
			maxValue = txInfo.Value
		}
	}

	return primaryTx
}

// signTransaction 为交易签名（需要用户签名信息）
// 参数说明：
//   - tx: 需要签名的交易对象
//   - rawTx: 包含用户签名信息的原始交易数据
func (pm *Paymaster) signTransaction(tx *solana.Transaction) error {
	// 使用统一的验证函数
	if err := validateTransactionObject(tx); err != nil {
		return err
	}

	// 序列化交易消息用于签名
	msgBytes, err := tx.Message.MarshalBinary()
	if err != nil {
		return wrapError("serialize transaction message", err)
	}

	// 使用统一的签名创建函数
	feePayerSig, err := pm.createPaymasterSignature(msgBytes)
	if err != nil {
		return wrapError("create paymaster signature", err)
	}

	// 确定哪些账户需要签名
	signers := pm.getRequiredSigners(tx.Message)

	// 设置交易签名
	if err := pm.setTransactionSignatures(tx, signers, feePayerSig); err != nil {
		return fmt.Errorf("set pay signer fail : %w", err)
	}

	// 验证所有签名
	if err := tx.VerifySignatures(); err != nil {
		return fmt.Errorf("varify signatures fail : %w", err)
	}

	return nil
}

// prepareTransaction 准备交易对象（保持向后兼容性）
// 参数：
//   - rawTxHex: 原始交易十六进制字符串
//
// 返回：
//   - *solana.Transaction: 准备好的交易对象
//   - error: 错误信息
func (pm *Paymaster) prepareTransaction(rawTxHex string) (*solana.Transaction, error) {
	// 使用统一的交易准备函数
	return prepareTransactionFromHex(rawTxHex)
}

// setTransactionSignatures 设置交易签名
// 参数说明：
//   - tx: 需要设置签名的交易对象
//   - signers: 需要签名的账户公钥列表
//   - feePayerSig: 费用支付者（paymaster）的签名
func (pm *Paymaster) setTransactionSignatures(tx *solana.Transaction, signers []solana.PublicKey,
	feePayerSig solana.Signature) error {
	// 验证签名参数
	if feePayerSig.IsZero() {
		return fmt.Errorf("fee payer signature is zero value")
	}
	// 获取paymaster公钥
	paymasterPub, err := pm.getPayPublic()
	if err != nil {
		return wrapError("get paymaster public key", err)
	}

	// 遍历签名者列表，为每个签名者设置对应的签名
	for i, signer := range signers {
		switch {
		case signer.Equals(paymasterPub):
			// 设置paymaster签名
			tx.Signatures[i] = feePayerSig
			pm.log.Debugf("已设置paymaster签名，索引: %d, 公钥: %s", i, signer.String())
		}
	}

	// 使用统一的签名验证函数
	if err := validateTransactionSignatures(tx, len(signers)); err != nil {
		return wrapError("validate transaction signatures", err)
	}

	pm.log.Infof("交易签名设置完成，共处理 %d 个签名，paymaster: %s",
		len(signers), paymasterPub.String())
	return nil
}
