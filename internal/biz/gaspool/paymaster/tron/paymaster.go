package tron

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/internal/biz/gaspool/base"
	"byd_wallet/model"
	"context"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"time"

	tronChain "byd_wallet/internal/biz/syncer/chain/tron"

	"github.com/alitto/pond/v2"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/api"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/core"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/proto"
)

type AsyncTaskMgr interface {
	AddWaitGasTask(ctx context.Context, txID uint) error
	AllWaitGasTask(ctx context.Context) ([]uint, error)
	RemoveWaitGasTask(ctx context.Context, txID uint) error

	AddWaitDepositTxTask(ctx context.Context, txID uint) error
	AllWaitDepositTxTask(ctx context.Context) ([]uint, error)
	RemoveWaitDepositTxTask(ctx context.Context, txID uint) error
}

type BuyBandwidthReq struct {
	ReceiveAddr string
	PledgeNum   decimal.Decimal
	PledgeHour  int
	ActivateFee decimal.Decimal
}

type BuyBandwidthReply struct {
	OrderID string
	Price   decimal.Decimal
	Amount  decimal.Decimal
}

type BuyEnergyReq struct {
	ReceiveAddr string
	PledgeNum   decimal.Decimal
	PledgeHour  int
}

type BuyEnergyReply struct {
	OrderID string
	Amount  decimal.Decimal // sun
	Price   decimal.Decimal // sun
}

type GetEnergyPriceReq struct {
	PledgeNum  decimal.Decimal
	PledgeHour int
}

type TransferTrxReq struct {
	ReceiveAddress string
	Amount         decimal.Decimal // sun
}

type TronBandwidthPayWallet interface {
	TransferTrx(ctx context.Context, req *TransferTrxReq) (txHash string, err error)
	EstimateTransferTrxBandwidth(ctx context.Context, req *TransferTrxReq) (bandwidth decimal.Decimal, err error)
}

type TronRentApi interface {
	GetEnergyPrice(ctx context.Context, req *GetEnergyPriceReq) (price decimal.Decimal, err error)
	BuyEnergy(ctx context.Context, req *BuyEnergyReq) (reply *BuyEnergyReply, err error)
}

type TronAddressChecker interface {
	IsActivatedAddress(ctx context.Context, address string) (bool, error)
}

type Paymaster struct {
	log *log.Helper

	tokenPriceReader       base.TokenPriceReader
	stxMgr                 base.GasPoolSponsorTxMgr
	gpMgr                  base.GasPoolMgr
	tronCli                *tronChain.RoundRobinClient
	tronBandwidthPayWallet TronBandwidthPayWallet
	tronRentApi            TronRentApi
	tronRentHour           int
	tronAddressChecker     TronAddressChecker
	asyncTaskMgr           AsyncTaskMgr
	statsService           base.GasPoolStatsService // gas pool统计服务

	stopCh                chan struct{}
	waitGasTaskPool       pond.Pool
	waitDepositTxTaskPool pond.Pool
}

func NewPaymaster(logger log.Logger,
	tokenPriceReader base.TokenPriceReader,
	stxMgr base.GasPoolSponsorTxMgr,
	gpMgr base.GasPoolMgr,
	tronCli *tronChain.RoundRobinClient,
	tronBandwidthPayWallet TronBandwidthPayWallet,
	tronRentApi TronRentApi,
	tronAddressChecker TronAddressChecker,
	asyncTaskMgr AsyncTaskMgr,
	statsService base.GasPoolStatsService,
) *Paymaster {
	return &Paymaster{
		log: log.NewHelper(logger),

		tokenPriceReader:       tokenPriceReader,
		stxMgr:                 stxMgr,
		gpMgr:                  gpMgr,
		tronCli:                tronCli,
		tronBandwidthPayWallet: tronBandwidthPayWallet,
		tronRentApi:            tronRentApi,
		tronRentHour:           1, // default: 1h
		tronAddressChecker:     tronAddressChecker,
		asyncTaskMgr:           asyncTaskMgr,
		statsService:           statsService,
	}
}

func (pm *Paymaster) isPriceTimeExpired(timeUnix int64) bool {
	return time.Now().Unix() > timeUnix+60 // TODO: config ???
}

func (pm *Paymaster) DecodeUserTx(ctx context.Context, rawTxHex string, txType model.GasPoolTxType) (*gaspool.UserTx, error) {
	// transaction
	tx, err := parseRawTxJson(rawTxHex)
	if err != nil {
		return nil, err
	}

	userTx := &gaspool.UserTx{}
	userTx.BroadcastTx = tx

	rawTxBytes, err := proto.Marshal(tx)
	if err != nil {
		return nil, fmt.Errorf("marshal raw tx error: %w", err)
	}
	userTx.RawTxBytes = rawTxBytes
	userTx.RawTxHex = hex.EncodeToString(userTx.RawTxBytes)

	// expiration
	if tx.RawData.Expiration < time.Now().Add(10*time.Second).UnixMilli() {
		return nil, errors.New("tx is expired")
	}

	// transaction raw
	txRaw := tx.RawData
	userTx.FeeLimit = decimal.NewFromInt(txRaw.FeeLimit)

	// tx hash
	txRawBts, err := proto.Marshal(txRaw)
	if err != nil {
		return nil, fmt.Errorf("marshal tx raw: %w", err)
	}
	hash := sha256.Sum256(txRawBts)
	userTx.TxHashBytes = hash[:]
	userTx.TxHash = hex.EncodeToString(userTx.TxHashBytes)

	// contract
	if len(txRaw.Contract) == 0 {
		return nil, errors.New("raw data contract is empty")
	}
	contract := txRaw.Contract[0]
	switch contract.Type {
	case core.Transaction_Contract_TransferContract:
		var data core.TransferContract
		if err = contract.GetParameter().UnmarshalTo(&data); err != nil {
			return nil, fmt.Errorf("unmarshal transfer contract: %v", err)
		}
		userTx.From = bytes2AddressString(data.GetOwnerAddress())
		userTx.To = bytes2AddressString(data.GetToAddress())
		userTx.Value = decimal.NewFromInt(data.GetAmount())
	case core.Transaction_Contract_TriggerSmartContract:
		var data core.TriggerSmartContract
		if err = contract.GetParameter().UnmarshalTo(&data); err != nil {
			return nil, fmt.Errorf("unmarshal trigger smart contract: %v", err)
		}
		userTx.From = bytes2AddressString(data.GetOwnerAddress())
		userTx.Contract = bytes2AddressString(data.GetContractAddress())
		methodSig := decodeTRC20MethodSignature(data.GetData())
		if methodSig != trc20TransferMethodSignature {
			return nil, fmt.Errorf("unknown method signature: %s", methodSig)
		}
		toAddr, value, err := decodeTRC20TransferData(data.GetData())
		if err != nil {
			return nil, fmt.Errorf("decode trc20 transfer data error: %v", err)
		}
		userTx.To = toAddr
		userTx.Value = decimal.NewFromBigInt(value, 0)
	default:
		return nil, fmt.Errorf("unknown contract type: %s", contract.Type)
	}

	// trx or trc20 price
	price, timeUnix, err := pm.tokenPriceReader.GetTokenLatestPriceUSDT(ctx, constant.TronChainIndex, "")
	if err != nil {
		return nil, fmt.Errorf("GetTokenLatestPriceUSDT: %w", err)
	}
	if pm.isPriceTimeExpired(timeUnix) {
		return nil, fmt.Errorf("tron price expired: %d", timeUnix)
	}
	if price.LessThanOrEqual(decimal.Zero) {
		return nil, errors.New("invalid trx price")
	}
	userTx.Price = price
	userTx.PriceTimeUnix = timeUnix

	// calculate value usdt
	if userTx.Contract == "" {
		userTx.ValueUSDT = userTx.Value.Div(decimalsTRX).Mul(userTx.Price).Mul(decimalsUSDT)
	} else {
		userTx.ValueUSDT = userTx.Value
	}

	userTx.TxType = txType
	userTx.ChainIndex = constant.TronChainIndex
	return userTx, nil
}

func (pm *Paymaster) VerifyUserTxSignature(ctx context.Context, tx *gaspool.UserTx) (bool, error) {
	if tx.BroadcastTx == nil {
		return false, errors.New("broadcast tx is nil")
	}
	btx, ok := tx.BroadcastTx.(*core.Transaction)
	if !ok {
		return false, errors.New("invalid broadcast tx")
	}
	if len(btx.Signature) == 0 {
		return false, errors.New("signature is empty")
	}
	return verifyTxSignature(tx.TxHashBytes, btx.Signature[0], tx.From)
}

func (pm *Paymaster) EstimateGas(ctx context.Context, tx *gaspool.UserTx) (*gaspool.UserTxGas, error) {
	switch tx.TxType {
	case model.GasPoolTxTypeTransfer,
		model.GasPoolTxTypeDepositPreReduceGas:
		return pm.estimateTransferGas(ctx, tx)
	default:
		return nil, fmt.Errorf("unknown tx type: %s", tx.TxType)
	}
}

func (pm *Paymaster) estimateTransferGas(ctx context.Context, tx *gaspool.UserTx) (gas *gaspool.UserTxGas, err error) {
	btx, ok := tx.BroadcastTx.(*core.Transaction)
	if !ok {
		return nil, fmt.Errorf("invalid broadcast tx type: %T", tx.BroadcastTx)
	}

	cli := pm.tronCli.Next()
	params, err := cli.Client.GetChainParameters(ctx, &api.EmptyMessage{})
	if err != nil {
		err = fmt.Errorf("GetChainParameters: %w", err)
		return
	}

	// chain params
	bandwidthPrice := decimal.Zero
	chainCreateNewAccountInSysFee := decimal.Zero
	chainCreateAccountFee := decimal.Zero
	f1, f2, f3 := false, false, false
	for _, param := range params.GetChainParameter() {
		if f1 && f2 && f3 {
			break
		}
		if param.Key == "getTransactionFee" {
			bandwidthPrice = decimal.NewFromInt(param.Value)
			f1 = true
			continue
		}
		if param.Key == "getCreateNewAccountFeeInSystemContract" {
			chainCreateNewAccountInSysFee = decimal.NewFromInt(param.Value)
			f2 = true
			continue
		}
		if param.Key == "getCreateAccountFee" {
			chainCreateAccountFee = decimal.NewFromInt(param.Value)
			f3 = true
			continue
		}
	}
	if !bandwidthPrice.IsPositive() {
		return nil, errors.New("invalid bandwidth price")
	}
	if !chainCreateNewAccountInSysFee.IsPositive() {
		return nil, errors.New("invalid chain create new account in system fee")
	}
	if !chainCreateAccountFee.IsPositive() {
		return nil, errors.New("invalid chain create account fee")
	}

	chainActivateFee := chainCreateNewAccountInSysFee.Add(chainCreateAccountFee)

	// bandwidth
	// // office calculate bandwidth:
	// RawTxBytes = proto.Marshal(core.Transaction{RawData: txRaw, Signature: [][]byte{sig}})
	// baseBandwidthCost = RawTxBytesSize+64
	baseBandwidthCost := decimal.NewFromInt(int64(len(tx.RawTxBytes) + 64))
	bandwidthCost := baseBandwidthCost
	nativeTxFee := decimal.Zero
	activateFee := decimal.Zero // biz defined account activate fee
	if tx.Contract == "" {
		// transfer trx
		ok, err := pm.tronAddressChecker.IsActivatedAddress(ctx, tx.To)
		if err != nil {
			return nil, fmt.Errorf("check activate status: %w", err)
		}
		if !ok {
			// receiver account is inactivated:
			// bandwidthCost=chainActivateFee/bandwidthPrice
			// nativeTxFee=TransferTrxFee(TransferTrx=bandwidthCost*bandwidthPrice)
			bandwidthCost = chainActivateFee.Div(bandwidthPrice)
		}
		// receiver account is activated:
		// bandwidthCost=baseBandwidthCost
		// nativeTxFee=TransferTrxFee(TransferTrx=bandwidthCost*bandwidthPrice)
	} else {
		// transfer trc20
		ok, err := pm.tronAddressChecker.IsActivatedAddress(ctx, tx.From)
		if err != nil {
			return nil, fmt.Errorf("check activate status: %w", err)
		}
		if !ok {
			// sender account is inactivated:
			// bandwidthCost=baseBandwidthCost
			// nativeTxFee=TransferTrxFee(TransferTrx=bandwidthCost*bandwidthPrice) + chainActivateFee
			nativeTxFee = nativeTxFee.Add(chainActivateFee)
			activateFee = chainActivateFee
		}
		// sender account is activated:
		// bandwidthCost=baseBandwidthCost
		// nativeTxFee=TransferTrxFee(TransferTrx=bandwidthCost*bandwidthPrice)
	}
	nativeTxValue := bandwidthCost.Mul(bandwidthPrice)
	transferBw, err := pm.tronBandwidthPayWallet.EstimateTransferTrxBandwidth(ctx, &TransferTrxReq{
		ReceiveAddress: tx.To,
		Amount:         nativeTxValue,
	})
	if err != nil {
		return nil, fmt.Errorf("estimateTransferTrxFee: %w", err)
	}
	if transferBw.IsNegative() {
		return nil, fmt.Errorf("invalid estimateTransferTrxFee result: %w", err)
	}
	nativeTxGas := transferBw.Mul(bandwidthPrice)
	nativeTxFee = nativeTxFee.Add(nativeTxGas)

	if bandwidthCost.IsNegative() {
		return nil, errors.New("invalid bandwidth cost")
	}
	if nativeTxFee.IsNegative() {
		return nil, errors.New("invalid native tx fee")
	}

	// energy
	energyCost := decimal.Zero
	if tx.Contract != "" {
		contract := btx.GetRawData().GetContract()
		if len(contract) == 0 {
			return nil, errors.New("invalid contract")
		}
		var data core.TriggerSmartContract
		if err = contract[0].GetParameter().UnmarshalTo(&data); err != nil {
			return nil, fmt.Errorf("unmarshal trigger smart contract: %w", err)
		}
		res, terr := cli.Client.EstimateEnergy(ctx, &core.TriggerSmartContract{
			OwnerAddress:    data.OwnerAddress,
			ContractAddress: data.ContractAddress,
			CallValue:       data.CallValue,
			Data:            data.Data,
		})
		if terr != nil {
			err = fmt.Errorf("EstimateEnergy: %w", terr)
			return
		}
		energyCost = decimal.NewFromInt(res.EnergyRequired)
	}

	// energy price
	energyPrice, err := pm.tronRentApi.GetEnergyPrice(ctx, &GetEnergyPriceReq{
		PledgeNum:  energyCost,
		PledgeHour: pm.tronRentHour,
	})
	if err != nil {
		err = fmt.Errorf("GetEnergyPrice: %w", err)
		return
	}
	if energyPrice.LessThanOrEqual(decimal.Zero) {
		return nil, errors.New("invalid energy price")
	}

	// check user account resources
	bandwidthBalance, energyBalance, terr := getAccountAvailableFee(pm.tronCli.Next(), tx.From)
	if terr != nil {
		err = fmt.Errorf("get account available fee: %w", terr)
		return
	}
	needBandwidth := bandwidthCost.GreaterThan(bandwidthBalance)
	needEnergy := energyCost.GreaterThan(energyBalance)
	if !needBandwidth &&
		!needEnergy {
		err = errors.New("user tx not allowed")
		return
	}
	if !needBandwidth {
		bandwidthCost = decimal.Zero
		nativeTxFee = decimal.Zero
		nativeTxValue = decimal.Zero
		nativeTxGas = decimal.Zero
		// ignore: activateFee = decimal.Zero // inactivated account bandwidth is zero
	}
	if !needEnergy {
		energyCost = decimal.Zero
	}

	// total cost
	gasTrxSun := bandwidthCost.Mul(bandwidthPrice).Add(energyCost.Mul(energyPrice)).Add(nativeTxFee)
	gasUSDT := gasTrxSun.Div(decimalsTRX).Mul(tx.Price).Mul(decimalsUSDT)

	if !gasUSDT.IsPositive() {
		err = errors.New("invalid gas usdt")
		return
	}

	gas = &gaspool.UserTxGas{
		Gas:            gasTrxSun,
		GasUSDT:        gasUSDT,
		Price:          tx.Price,
		PriceTimeUnix:  tx.PriceTimeUnix,
		Bandwidth:      bandwidthCost,
		BandwidthPrice: bandwidthPrice,
		Energy:         energyCost,
		EnergyPrice:    energyPrice,
		ActivateFee:    activateFee,
		NativeTxFee:    nativeTxFee,
		NativeTxValue:  nativeTxValue,
		NativeTxGas:    nativeTxGas,
	}
	return
}

func (pm *Paymaster) updateTxFailStatusWithFee(ctx context.Context,
	tx *model.GasPoolSponsorTx,
	update *model.GasPoolSponsorTx,
	failErr error,
	usedTrx decimal.Decimal) error {
	var refundFlowID uint
	if tx.ReduceFlowID > 0 {
		usedUSDT := usedTrx.Div(decimalsTRX).Mul(tx.Price).Mul(decimalsUSDT)
		refundAmount := tx.GasUSDT.Sub(usedUSDT)
		if refundAmount.IsPositive() {
			flow, err := pm.gpMgr.RefundGasPool(ctx, tx.ReduceFlowID, refundAmount)
			if err != nil {
				pm.log.Errorf("refund gas pool err: %v: reduceFlowID=%d, refundAmount=%s", err, tx.ReduceFlowID, refundAmount.String())
			} else {
				refundFlowID = flow.ID
			}
		}
	}
	if update == nil {
		update = &model.GasPoolSponsorTx{}
	}
	update.ID = tx.ID
	update.Status = model.GasPoolTxStatusFail
	update.Reason = failErr.Error()
	update.RefundFlowID = refundFlowID
	return pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
}

func (pm *Paymaster) SendDepositTx(ctx context.Context, stx *model.GasPoolSponsorTx) (err error) {
	if stx.BroadcastTx == nil {
		err = errors.New("broadcast tx is nil")
		return
	}
	tx, ok := stx.BroadcastTx.(*core.Transaction)
	if !ok {
		err = errors.New("invalid broadcast tx")
		return
	}
	cli := pm.tronCli.Next()
	bResp, err := cli.Broadcast(tx)
	if err != nil {
		err = fmt.Errorf("broadcast tx error: %w", err)
		return
	}

	update := &model.GasPoolSponsorTx{}
	update.ID = stx.ID

	// broadcast fail
	if !bResp.Result {
		err = fmt.Errorf("broadcast tx fail: %s", bResp.String())
		update.Reason = "broadcast fail:" + bResp.String()
		update.Status = model.GasPoolTxStatusFail
		terr := pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
		if terr != nil {
			pm.log.Errorf("UpdateGasPoolSponsorTx by broadcast fail: %v: txID=%d, status=%s",
				err, update.ID, update.Status)
		}
		return
	}

	update.Status = model.GasPoolTxStatusPending
	if err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update); err != nil {
		pm.log.Errorf("UpdateGasPoolSponsorTx by wait deposit tx: %v: update=%+v", err, update)
		return
	}

	err = pm.asyncTaskMgr.AddWaitDepositTxTask(ctx, stx.ID)
	if err != nil {
		pm.log.Errorf("AddWaitDepositTxTask: %v: txID=%d", err, stx.ID)
	}
	return
}

func (pm *Paymaster) updateTxFailStatusWithoutFee(ctx context.Context, id uint, failErr error) error {
	update := &model.GasPoolSponsorTx{}
	update.ID = id
	update.Status = model.GasPoolTxStatusFail
	update.Reason = failErr.Error()
	return pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
}

func (pm *Paymaster) SendSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) (err error) {
	update := &model.GasPoolSponsorTx{}
	update.ID = tx.ID

	if tx.Bandwidth.IsPositive() {
		trxTxHash, terr := pm.tronBandwidthPayWallet.TransferTrx(ctx, &TransferTrxReq{
			Amount:         tx.NativeTxValue,
			ReceiveAddress: tx.From,
		})
		if terr != nil {
			err = fmt.Errorf("buy bandwidth error: %w", terr)
			if updateErr := pm.updateTxFailStatusWithoutFee(ctx, tx.ID, err); updateErr != nil {
				pm.log.Errorf("update tx fail status without fee error: %v: txID=%d, reason=%v",
					updateErr, tx.ID, err)
			}
			return
		}
		update.NativeTxHash = trxTxHash
	}
	remainLogicFunc := func(ctx context.Context) (err error) {
		if tx.Energy.IsPositive() {
			reply, terr := pm.tronRentApi.BuyEnergy(ctx, &BuyEnergyReq{
				PledgeHour:  pm.tronRentHour,
				PledgeNum:   tx.Energy,
				ReceiveAddr: tx.From,
			})
			if terr != nil {
				err = fmt.Errorf("buy energy error: %w", terr)
				if updateErr := pm.updateTxFailStatusWithFee(ctx, tx, update, err, update.NativeTxFee); updateErr != nil {
					pm.log.Errorf("update tx fail status with fee error: %v: txID=%d, reason=%v",
						updateErr, tx.ID, err)
				}
				return
			}
			update.RentEnergyOrderID = reply.OrderID
			update.RentEnergyFee = reply.Amount
			update.ActualEnergyPrice = reply.Price
		}

		update.Status = model.GasPoolTxStatusWaitGas
		if err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update); err != nil {
			pm.log.Errorf("UpdateGasPoolSponsorTx by wait gas: %v: update=%+v", err, update)
			return
		}
		if err = pm.asyncTaskMgr.AddWaitGasTask(ctx, tx.ID); err != nil {
			pm.log.Errorf("AddWaitGasTask: %v: txID=%d", err, tx.ID)
		}
		return
	}

	if tx.ActivateFee.IsPositive() &&
		tx.Energy.IsPositive() {
		go func() {
			pm.log.Infof("inactivated account tx wait 3 seconds: txID=%d", tx.ID)
			time.Sleep(time.Second * 3)                  // wait a block duration
			err := remainLogicFunc(context.Background()) // buy energy after activate account
			if err != nil {
				pm.log.Errorf("last logic func: %v", err)
			}
		}()
		return nil
	}
	return remainLogicFunc(ctx)
}

// --- async task
func (pm *Paymaster) handleWaitDepositTxTask(ctx context.Context, taskExpireSecs int64) {
	ids, err := pm.asyncTaskMgr.AllWaitDepositTxTask(ctx)
	if err != nil {
		pm.log.Errorf("AllWaitDepositTxTask: %v", err)
		return
	}
	if len(ids) == 0 {
		return
	}
	pm.log.Infof("handleWaitDepositTxTask: count=%d", len(ids))
	g := pm.waitDepositTxTaskPool.NewGroup()

	nowUnix := time.Now().Unix()
	for _, id := range ids {
		stx, err := pm.stxMgr.FindGasPoolSponsorTxByID(ctx, id)
		if err != nil {
			pm.log.Errorf("get sponsor tx error: %v", err)
			terr := pm.asyncTaskMgr.RemoveWaitDepositTxTask(ctx, id)
			if terr != nil {
				pm.log.Errorf("RemoveWaitDepositTxTask by db fail: %v: id=%d", terr, id)
			}
			return
		}

		spent := nowUnix - stx.CreatedAt.Unix()
		if spent >= taskExpireSecs {
			pm.log.Errorf("wait deposit tx task is expired: id=%d", id)
			err := pm.asyncTaskMgr.RemoveWaitDepositTxTask(ctx, id)
			if err != nil {
				pm.log.Errorf("RemoveWaitDepositTxTask by expired: %v: id=%d", err, id)
			}
			return
		}

		if stx.Status != model.GasPoolTxStatusPending {
			err := pm.asyncTaskMgr.RemoveWaitDepositTxTask(ctx, id)
			if err != nil {
				pm.log.Errorf("RemoveWaitDepositTxTask by useless tx: %v: id=%d", err, id)
			}
			return
		}

		cli := pm.tronCli.Next()
		tx, err := cli.GetTransactionByID(stx.TxHash)
		if err != nil {
			pm.log.Errorf("GetTransactionByID: %v: id=%d", err, id)
			return
		}

		if len(tx.Ret) == 0 {
			return
		}

		if tx.Ret[0].Ret != core.Transaction_Result_SUCESS {
			update := &model.GasPoolSponsorTx{}
			update.ID = stx.ID
			update.Reason = "tx execute result fail"
			update.Status = model.GasPoolTxStatusFail
			err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
			if err != nil {
				pm.log.Errorf("UpdateGasPoolSponsorTx by tx execute result fail: %v: txID=%d",
					err, update.ID)
			}
			return
		}

		// if trc20 transfer, check contract result
		if stx.Contract != "" &&
			tx.Ret[0].ContractRet != core.Transaction_Result_SUCCESS {
			update := &model.GasPoolSponsorTx{}
			update.ID = stx.ID
			update.Reason = "contract execute result fail"
			update.Status = model.GasPoolTxStatusFail
			err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
			if err != nil {
				pm.log.Errorf("UpdateGasPoolSponsorTx by contract execute result fail: %v: txID=%d",
					err, update.ID)
			}
			return
		}

		err = pm.asyncTaskMgr.RemoveWaitDepositTxTask(ctx, id)
		if err != nil {
			pm.log.Errorf("RemoveWaitDepositTxTask by finished: %v: id=%d, txType=%s",
				err, id, stx.TxType)
		}

		var depositAmount = decimal.Zero
		switch stx.TxType {
		case model.GasPoolTxTypeDeposit:
			depositAmount = stx.ValueUSDT
		case model.GasPoolTxTypeDepositPreReduceGas:
			depositAmount = stx.ValueUSDT.Sub(stx.GasUSDT)
		default:
			return
		}

		// deposit
		flow, err := pm.gpMgr.DepositGasPool(ctx, stx.UserID, depositAmount, stx.ChainIndex, stx.TxHash)
		if err != nil {
			pm.log.Errorf("DepositGasPool by sendRawTxByDepositWithoutGas: %v: userID=%d, depositAmount=%s, chainIndex=%d, txHash=%s",
				err, stx.UserID, depositAmount, stx.ChainIndex, stx.TxHash)
			return
		}

		// finished
		update := &model.GasPoolSponsorTx{}
		update.ID = stx.ID
		update.DepositFlowID = flow.ID
		update.Status = model.GasPoolTxStatusSuccess
		err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
		if err != nil {
			pm.log.Errorf("UpdateGasPoolSponsorTx by finished: %v: txID=%d, status=%s", err, update.ID, update.Status)
		}
	}
	_ = g.Wait()
}
func (pm *Paymaster) handleWaitGasTask(ctx context.Context, taskExpireSecs int64) {
	ids, err := pm.asyncTaskMgr.AllWaitGasTask(ctx)
	if err != nil {
		pm.log.Errorf("AllWaitGasTask: %v", err)
		return
	}
	if len(ids) == 0 {
		return
	}
	pm.log.Infof("handleWaitGasTask: count=%d", len(ids))

	g := pm.waitGasTaskPool.NewGroup()

	nowUnix := time.Now().Unix()
	for _, id := range ids {
		g.Submit(func() {
			stx, err := pm.stxMgr.FindGasPoolSponsorTxByID(ctx, id)
			if err != nil {
				pm.log.Errorf("get sponsor tx error: %v", err)
				terr := pm.asyncTaskMgr.RemoveWaitGasTask(ctx, id)
				if terr != nil {
					pm.log.Errorf("RemoveWaitGasTask by db fail: %v: id=%d", terr, id)
				}
				return
			}

			spent := nowUnix - stx.CreatedAt.Unix()
			if spent >= taskExpireSecs {
				pm.log.Errorf("wait gas task is expired: id=%d", id)
				err := pm.asyncTaskMgr.RemoveWaitGasTask(ctx, id)
				if err != nil {
					pm.log.Errorf("RemoveWaitGasTask by expired: %v: id=%d", err, id)
				}
				return
			}

			if stx.Status != model.GasPoolTxStatusWaitGas {
				err := pm.asyncTaskMgr.RemoveWaitGasTask(ctx, id)
				if err != nil {
					pm.log.Errorf("RemoveWaitGasTask by useless tx: %v: id=%d", err, id)
				}
				return
			}

			cli := pm.tronCli.Next()
			bandwidthBalance, energyBalance, err := getAccountAvailableFee(cli, stx.From)
			if err != nil {
				pm.log.Errorf("get account available fee error, %v", err)
				return
			}
			pm.log.Infof("get account available fee: bandwidth=%s, energy=%s, stx.Energy=%s, stx.Bandwidth=%s, stx.NativeTxFee=%s",
				bandwidthBalance, energyBalance, stx.Energy, stx.Bandwidth, stx.NativeTxFee)
			if energyBalance.LessThan(stx.Energy) {
				return
			}
			if bandwidthBalance.LessThan(stx.Bandwidth) {
				if !stx.NativeTxFee.IsPositive() {
					return
				}
				balance, err := getAccountBalance(cli, stx.From)
				if err != nil {
					pm.log.Errorf("get account balance error, %v", err)
					return
				}
				pm.log.Infof("get account balance: balance=%s, stx.BandwidthPrice=%s, stx.Bandwidth=%s",
					balance, stx.BandwidthPrice, stx.Bandwidth)

				if balance.Div(stx.BandwidthPrice).LessThan(stx.Bandwidth) {
					return
				}
			}

			rawTxBytes, err := hex.DecodeString(stx.RawTxHex)
			if err != nil {
				pm.log.Errorf("decode raw tx hex error: %v", err)
				return
			}
			var tx core.Transaction
			err = proto.Unmarshal(rawTxBytes, &tx)
			if err != nil {
				pm.log.Errorf("unmarshal tx raw error: %v", err)
				return
			}

			terr := pm.asyncTaskMgr.RemoveWaitGasTask(ctx, id)
			if terr != nil {
				pm.log.Errorf("RemoveWaitGasTask by finished: %v: id=%d", terr, id)
			}

			update := &model.GasPoolSponsorTx{}
			update.ID = stx.ID

			res2, err := cli.Broadcast(&tx)
			if err != nil {
				pm.log.Errorf("broadcast tx error: %v", err)
				update.Reason = "broadcast invoke fail:" + err.Error()
				update.Status = model.GasPoolTxStatusFail
				err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
				if err != nil {
					pm.log.Errorf("UpdateGasPoolSponsorTx by broadcast invoke fail: %v: txID=%d, reason=%s",
						err, update.ID, update.Reason)
				}
				return
			}

			// broadcast fail
			if !res2.Result {
				update.Reason = "broadcast result fail:" + res2.String()
				update.Status = model.GasPoolTxStatusFail
				err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
				if err != nil {
					pm.log.Errorf("UpdateGasPoolSponsorTx by broadcast result fail: %v: txID=%d, reason=%s",
						err, update.ID, update.Reason)
				}
				return
			}

			// wait deposit tx
			if stx.TxType == model.GasPoolTxTypeDepositPreReduceGas {
				update.Status = model.GasPoolTxStatusPending
				if err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update); err != nil {
					pm.log.Errorf("UpdateGasPoolSponsorTx by wait deposit tx: %v: update=%+v", err, update)
					return
				}

				err = pm.asyncTaskMgr.AddWaitDepositTxTask(ctx, stx.ID)
				if err != nil {
					pm.log.Errorf("AddWaitDepositTxTask: %v: txID=%d", err, stx.ID)
				}
				return
			}

			// finished
			update.Status = model.GasPoolTxStatusSuccess

			// 记录gas pool使用统计（仅对消耗gas pool的交易类型）
			if stx.TxType.IsUseGasPool() && stx.GasUSDT.IsPositive() {
				if err := pm.statsService.RecordGasUsage(ctx, stx.UserID, stx.GasUSDT); err != nil {
					pm.log.Errorf("Tron链记录gas pool使用统计失败: %v，用户ID: %d，消耗金额: %s",
						err, stx.UserID, stx.GasUSDT.String())
					// 统计记录失败不影响主流程，继续执行
				} else {
					pm.log.Debugf("Tron链成功记录gas pool使用统计，用户ID: %d，消耗金额: %s USDT",
						stx.UserID, stx.GasUSDT.String())
				}
			}

			err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
			if err != nil {
				pm.log.Errorf("UpdateGasPoolSponsorTx by finished: %v: txID=%d, status=%s", err, update.ID, update.Status)
			}
		})
	}
	_ = g.Wait()
}

func (pm *Paymaster) Start(ctx context.Context) error {
	if pm.stopCh == nil {
		pm.stopCh = make(chan struct{})
	}
	if pm.waitGasTaskPool == nil {
		pm.waitGasTaskPool = pond.NewPool(10)
	}
	if pm.waitDepositTxTaskPool == nil {
		pm.waitDepositTxTaskPool = pond.NewPool(10)
	}

	go func() {
		const taskExpireSecs int64 = 120 // required: frontend build tx expiration duration greater than this value
		interval := 6 * time.Second      // chain: 3s/block
		t := time.NewTimer(interval)
		defer t.Stop()

		pm.log.Infof("start tron paymaster wait gas task: interval=%s, taskExpireSecs=%d",
			interval.String(), taskExpireSecs)

		for {
			t.Reset(interval)
			select {
			case <-pm.stopCh:
				pm.log.Info("stop tron paymaster wait gas task")
				return
			case <-t.C:
				pm.handleWaitGasTask(ctx, taskExpireSecs)
			}
		}
	}()
	go func() {
		const taskExpireSecs int64 = 120 // TODO: config???
		interval := time.Second
		t := time.NewTimer(interval)
		defer t.Stop()

		pm.log.Infof("start tron paymaster wait deposit tx task: interval=%s, taskExpireSecs=%d",
			interval.String(), taskExpireSecs)

		for {
			t.Reset(interval)
			select {
			case <-pm.stopCh:
				pm.log.Info("stop tron paymaster wait deposit tx task")
				return
			case <-t.C:
				pm.handleWaitDepositTxTask(ctx, taskExpireSecs)
			}
		}
	}()
	return nil
}

func (pm *Paymaster) Stop(ctx context.Context) error {
	if pm.stopCh != nil {
		close(pm.stopCh)
	}
	return nil
}
