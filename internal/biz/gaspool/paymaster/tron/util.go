package tron

import (
	"byd_wallet/utils"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"math/big"
	"strings"

	tCommon "github.com/fbsobreira/gotron-sdk/pkg/common"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/core"
	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/proto"

	tronChain "byd_wallet/internal/biz/syncer/chain/tron"

	"github.com/btcsuite/btcutil/base58"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/fbsobreira/gotron-sdk/pkg/address"
)

func bytes2AddressString(b []byte) string {
	return address.Address(b).String()
}

func decodeTRC20TransferData(data []byte) (toAddress string, value *big.Int, err error) {
	if len(data) != 4+32*2 {
		err = fmt.Errorf("invalid trc20 transfer data length")
		return
	}

	addressParam := data[4:36]
	addressBytes := addressParam[12:32]

	toAddress = base58.CheckEncode(addressBytes, 0x41)

	valueParam := data[36:68]
	value = new(big.Int).SetBytes(valueParam)
	return
}

func decodeTRC20ApprovalData(data []byte) (spender string, value *big.Int, err error) {
	if len(data) != 4+32*2 {
		err = fmt.Errorf("invalid trc20 approval data length")
		return
	}

	spenderParam := data[4:36]
	spenderBytes := spenderParam[12:32]

	spender = base58.CheckEncode(spenderBytes, 0x41)

	valueParam := data[36:68]
	value = new(big.Int).SetBytes(valueParam)
	return
}

func decodeTRC20MethodSignature(data []byte) string {
	return tCommon.BytesToHexString(data[:4])
}

func verifyTxSignature(txHash, signature []byte, ownerAddress string) (bool, error) {
	if len(txHash) != 32 {
		return false, fmt.Errorf("tx hash length must be 32 bytes")
	}
	if len(signature) != 65 {
		return false, fmt.Errorf("signature length must be 65 bytes")
	}
	// Fix V
	if signature[64] >= 27 {
		signature[64] -= 27
	}

	pubKey, err := crypto.SigToPub(txHash, signature)
	if err != nil {
		return false, fmt.Errorf("recover pubkey: %v", err)
	}
	addr1 := utils.TronHexAddressFromPubKey(pubKey)
	addr := utils.Base58CheckEncodeTRON(addr1)
	return addr == ownerAddress, nil
}

func getAccountAvailableFee(cli *tronChain.GRPCClient, address string) (bandwidth, energy decimal.Decimal, err error) {
	res, err := cli.GetAccountResource(address)
	if err != nil {
		err = fmt.Errorf("get account resource error: %w", err)
		return
	}
	b1 := res.FreeNetLimit - res.FreeNetUsed
	b2 := res.NetLimit - res.NetUsed
	bandwidth = decimal.NewFromInt(b1 + b2)
	energy = decimal.NewFromInt(res.EnergyLimit - res.EnergyUsed)
	return
}

func getAccountBalance(cli *tronChain.GRPCClient, address string) (balance decimal.Decimal, err error) {
	acc, err := cli.GetAccount(address)
	if err != nil {
		err = fmt.Errorf("get account error: %w", err)
		return
	}
	balance = decimal.NewFromInt(acc.Balance)
	return
}

type rawTxJson struct {
	Signature  []string `json:"signature"`
	RawDataHex string   `json:"raw_data_hex"`
}

func parseRawTxJson(rawTxJsonStr string) (tx *core.Transaction, err error) {
	rtxj := &rawTxJson{}
	err = json.Unmarshal([]byte(rawTxJsonStr), &rtxj)
	if err != nil {
		return nil, fmt.Errorf("parse raw tx fail: %w", err)
	}

	if len(rtxj.Signature) == 0 {
		return nil, errors.New("signature is empty")
	}

	sigStr := strings.TrimPrefix(rtxj.Signature[0], "0x")

	sig, err := hex.DecodeString(sigStr)
	if err != nil {
		return nil, fmt.Errorf("decode signature error: %w", err)
	}

	txRawBytes, err := hex.DecodeString(rtxj.RawDataHex)
	if err != nil {
		return nil, fmt.Errorf("decode raw data hex: %w", err)
	}
	txRaw := &core.TransactionRaw{}
	err = proto.Unmarshal(txRawBytes, txRaw)
	if err != nil {
		return nil, fmt.Errorf("unmarshal tx raw: %w", err)
	}
	return &core.Transaction{
		RawData:   txRaw,
		Signature: [][]byte{sig},
	}, nil
}
