package biz

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/keyunpacker"
	"byd_wallet/model"
	"context"
	"errors"
)

type KeyUnpacker interface {
	Unpack(chainIndex int64, key string) (*model.KeyInfo, error)
}

type KeyInfoRepo interface {
	GetPrivateKey(ctx context.Context, address string) (string, error)
	CreateKeyInfo(ctx context.Context, keyInfo *model.KeyInfo) error
}

type KeyInfoUsecase struct {
	evmUnpacker KeyUnpacker
	repo        KeyInfoRepo
}

func NewKeyInfoUsecase(evmUnpacker *keyunpacker.EVM, repo KeyInfoRepo) *KeyInfoUsecase {
	return &KeyInfoUsecase{evmUnpacker: evmUnpacker, repo: repo}
}

func (k *KeyInfoUsecase) ImportPrivateKey(ctx context.Context, chainIndex int64, key string) (*model.KeyInfo, error) {
	if !constant.IsEVMChain(chainIndex) {
		return nil, errors.New("chain index is not evm chain")
	}
	ki, err := k.evmUnpacker.Unpack(chainIndex, key)
	if err != nil {
		return nil, err
	}
	if err = k.repo.CreateKeyInfo(ctx, ki); err != nil {
		return nil, err
	}
	return ki, nil
}
