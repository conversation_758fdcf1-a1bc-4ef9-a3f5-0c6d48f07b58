package keyunpacker

import (
	"byd_wallet/internal/biz/chain/evm"
	"byd_wallet/model"
	"github.com/ethereum/go-ethereum/crypto"
)

type EVM struct{}

func NewEVM() *EVM {
	return &EVM{}
}

func (E *EVM) Unpack(chainIndex int64, key string) (*model.KeyInfo, error) {
	priv, err := evm.HexToECDSA(key)
	if err != nil {
		return nil, err
	}
	keyAddr := crypto.PubkeyToAddress(priv.PublicKey)
	return &model.KeyInfo{
		ChainIndex: chainIndex,
		Address:    keyAddr.Hex(),
		PrivateKey: key,
	}, nil
}
