package biz

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/chain/evm/erc20"
	"byd_wallet/model"
	"context"
	"errors"
	"fmt"
)

type TransferAdapter interface {
	Transfer(ctx context.Context, chainIndex int64, privateKey, tokenAddress, to, value string, dryrun ...bool) (*model.Transaction, error)
}

type Transactor struct {
	keyRepo    KeyInfoRepo
	evmAdapter TransferAdapter
}

func NewTransactor(keyRepo KeyInfoRepo, evm *erc20.Transactor) *Transactor {
	return &Transactor{keyRepo: keyRepo, evmAdapter: evm}
}

func (t *Transactor) Transfer(ctx context.Context, token *model.TokenAsset, from, to, value string, dryrun ...bool) (*model.Transaction, error) {
	if !constant.IsEVMChain(token.ChainIndex) {
		return nil, fmt.Errorf("chain not supported: %d", token.ChainIndex)
	}
	pk, err := t.keyRepo.GetPrivateKey(ctx, from)
	if err != nil {
		return nil, err
	}
	if token.TokenType == constant.NativeTokenType {
		return nil, errors.New("native token not supported")
	}
	return t.evmAdapter.Transfer(ctx, token.ChainIndex, pk, token.Address, to, value, dryrun...)
}
