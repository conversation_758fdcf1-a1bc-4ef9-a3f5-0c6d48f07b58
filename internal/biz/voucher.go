package biz

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/base"
	"byd_wallet/model"
	"context"
	"errors"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

var (
	ErrVoucherWasRedeemed = errors.New("voucher was redeemed")
)

type VoucherRecordRepo interface {
	ExistsVoucherRecord(ctx context.Context, code string) (bool, error)
	CreateVoucherRecord(ctx context.Context, v *model.VoucherRecord) error
	UpdateVoucherRecordPending(ctx context.Context, id uint) error
	UpdateVoucherRecordSuccess(ctx context.Context, id uint, txAt *time.Time) error
	UpdateVoucherRecordFail(ctx context.Context, id uint, errMsg string) error
	GetVoucherRecord(ctx context.Context, id uint) (*model.VoucherRecord, error)
	UpdateVoucherRecordTxInfo(ctx context.Context, id uint, hash, method string) error
	ListPendingVoucherRecords(ctx context.Context) ([]*model.VoucherRecord, error)
	PagedVoucherRecordsByAddresses(ctx context.Context, addresses []string, pagination base.Pagination) ([]*model.VoucherRecord, int64, error)
}

type VoucherConfigRepo interface {
	GetVoucherConfig(ctx context.Context, chainIndex int64) (*model.VoucherConfig, error)
}

type VoucherRepo interface {
	ListToken(ctx context.Context) ([]*model.TokenAsset, error)
	GetVoucher(ctx context.Context, nickname, code string) (*Voucher, error)
	WriteOff(ctx context.Context, nickname, code string) error
}

// Voucher 兑换券
type Voucher struct {
	// 面值
	Value      string
	TokenAsset *model.TokenAsset
	Used       bool
}

type VoucherUsecase struct {
	log         *log.Helper
	repo        VoucherRecordRepo
	userRepo    UserAddressRepo
	voucherRepo VoucherRepo
	configRepo  VoucherConfigRepo
	publisher   EventPublisher
	transactor  *Transactor
	txRepo      TransactionRepo
	refreshLock sync.Mutex
	chainRepo   BlockchainNetworkRepo
}

func NewVoucherUsecase(
	logger log.Logger,
	repo VoucherRecordRepo,
	userRepo UserAddressRepo,
	voucherRepo VoucherRepo,
	configRepo VoucherConfigRepo,
	publisher EventPublisher,
	transactor *Transactor,
	txRepo TransactionRepo,
	chainRepo BlockchainNetworkRepo,
) *VoucherUsecase {
	return &VoucherUsecase{
		log:         log.NewHelper(logger),
		repo:        repo,
		userRepo:    userRepo,
		voucherRepo: voucherRepo,
		configRepo:  configRepo,
		publisher:   publisher,
		transactor:  transactor,
		txRepo:      txRepo,
		chainRepo:   chainRepo,
	}
}

type RedeemInput struct {
	// 兑换码
	Code string
	// 昵称
	Nickname   string
	ChainIndex int64
	// 用户地址
	Address string
}

type VoucherRecord struct {
	ID uint `json:"id"`
}

func (v *VoucherUsecase) GetVoucher(ctx context.Context, nickname, code string) (*Voucher, error) {
	return v.voucherRepo.GetVoucher(ctx, nickname, code)
}

func (v *VoucherUsecase) ListToken(ctx context.Context) ([]*model.TokenAsset, error) {
	return v.voucherRepo.ListToken(ctx)
}

func (v *VoucherUsecase) PagedVoucherRecordsByAddresses(ctx context.Context, addresses []string, pagination base.Pagination) ([]*model.VoucherRecord, int64, error) {
	return v.repo.PagedVoucherRecordsByAddresses(ctx, addresses, pagination)
}

func (v *VoucherUsecase) RedeemVoucher(ctx context.Context, in *RedeemInput) error {
	conf, err := v.configRepo.GetVoucherConfig(ctx, in.ChainIndex)
	if err != nil {
		return err
	}
	chain, err := v.chainRepo.FindByIndex(ctx, in.ChainIndex)
	if err != nil {
		return err
	}
	exists, err := v.repo.ExistsVoucherRecord(ctx, in.Code)
	if err != nil {
		return err
	}
	if exists {
		return ErrVoucherWasRedeemed
	}
	user, err := v.userRepo.FindByChainIndexAndAddress(ctx, in.ChainIndex, in.Address)
	if err != nil {
		return err
	}
	voucher, err := v.voucherRepo.GetVoucher(ctx, in.Nickname, in.Code)
	if err != nil {
		return err
	}
	if voucher.Used {
		return ErrVoucherWasRedeemed
	}
	record := &model.VoucherRecord{
		UserID:       user.UserID,
		Nickname:     in.Nickname,
		Code:         in.Code,
		VoucherValue: voucher.Value,
		TokenAssetID: voucher.TokenAsset.ID,
		FromAddress:  conf.PlatformAddress,
		ToAddress:    user.Address,
		Value:        voucher.Value,
		Status:       model.VoucherStatusInit,
		Dryrun:       conf.Dryrun,
		ChainIndex:   voucher.TokenAsset.ChainIndex,
		ChainName:    chain.ChainName,
		Symbol:       voucher.TokenAsset.Symbol,
		TokenAddress: voucher.TokenAsset.Address,
		Decimals:     voucher.TokenAsset.Decimals,
	}
	if err := v.repo.CreateVoucherRecord(ctx, record); err != nil {
		return err
	}

	return v.publisher.Publish(ctx, &VoucherRecord{ID: record.ID})
}

func (v *VoucherUsecase) ProcessVoucherRedemption(ctx context.Context, id uint) error {
	record, err := v.repo.GetVoucherRecord(ctx, id)
	if err != nil {
		return err
	}
	if err = v.repo.UpdateVoucherRecordPending(ctx, id); err != nil {
		return err
	}
	tx, err := v.transactor.Transfer(ctx, record.TokenAsset, record.FromAddress, record.ToAddress, record.Value, record.Dryrun)
	if err != nil {
		if uErr := v.repo.UpdateVoucherRecordFail(ctx, id, err.Error()); uErr != nil {
			v.log.Errorf("UpdateVoucherRecordFail err: %v", uErr)
		}
		return err
	}

	return v.repo.UpdateVoucherRecordTxInfo(ctx, record.ID, tx.TxHash, tx.Method)
}

func (v *VoucherUsecase) ProcessPendingVoucherRecords(ctx context.Context) error {
	if !v.refreshLock.TryLock() {
		return nil
	}
	defer v.refreshLock.Unlock()
	records, err := v.repo.ListPendingVoucherRecords(ctx)
	if err != nil {
		return err
	}
	for _, record := range records {
		if err := v.ProcessPendingVoucherRecord(ctx, record); err != nil {
			return err
		}
	}
	return nil
}

func (v *VoucherUsecase) ProcessPendingVoucherRecord(ctx context.Context, record *model.VoucherRecord) error {
	if record.TokenAsset == nil || record.TxHash == "" {
		return nil
	}
	if record.Dryrun {
		if err := v.voucherRepo.WriteOff(ctx, record.Nickname, record.Code); err != nil {
			return err
		}
		now := time.Now()
		return v.repo.UpdateVoucherRecordSuccess(ctx, record.ID, &now)
	}
	tx, err := v.txRepo.GetByHash(ctx, record.TokenAsset.ChainIndex, record.TxHash)
	if err != nil {
		return err
	}
	if tx == nil {
		return nil
	}
	switch tx.Status {
	case constant.TransactionStatusSuccess:
		if err = v.voucherRepo.WriteOff(ctx, record.Nickname, record.Code); err != nil {
			return err
		}
		return v.repo.UpdateVoucherRecordSuccess(ctx, record.ID, tx.TxAt())
	case constant.TransactionStatusFail:
		return v.repo.UpdateVoucherRecordFail(ctx, record.ID, "")
	default:
		return nil
	}
}
