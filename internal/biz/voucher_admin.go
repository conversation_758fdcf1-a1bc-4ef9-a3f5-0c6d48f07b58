package biz

import (
	"byd_wallet/internal/biz/base"
	"byd_wallet/model"
	"context"
)

type VoucherRecordAdminFilter struct {
	ChainIndex int64
	From, To   string
	Pagination base.Pagination
}

type VoucherStats struct {
	// 统计币种
	StatsSymbol string `json:"stats_symbol"`
	// token精度
	Decimals int64 `json:"decimals"`
	// 今日转出
	ValueOfToday string `json:"value_of_today"`
	// 今日兑换笔数
	CountOfToday int64 `json:"count_of_today"`
	// 历史转出
	TotalValue string `json:"total_value"`
	// 历史兑换笔数
	TotalCount int64 `json:"total_count"`
}

type VoucherRecordAdminRepo interface {
	ListVoucherRecordByFilter(ctx context.Context, filter VoucherRecordAdminFilter) ([]*model.VoucherRecord, int64, error)
	VoucherStats(ctx context.Context) ([]*VoucherStats, error)
}

type VoucherAdminUsecase struct {
	repo VoucherRecordAdminRepo
}

func NewVoucherAdminUsecase(repo VoucherRecordAdminRepo) *VoucherAdminUsecase {
	return &VoucherAdminUsecase{repo: repo}
}

func (v VoucherAdminUsecase) VoucherStats(ctx context.Context) (*VoucherStats, error) {
	list, err := v.repo.VoucherStats(ctx)
	if err != nil {
		return nil, err
	}
	if list == nil {
		return &VoucherStats{}, nil
	}
	return list[0], nil
}

func (v VoucherAdminUsecase) ListVoucherRecordByFilter(ctx context.Context, filter VoucherRecordAdminFilter) ([]*model.VoucherRecord, int64, error) {
	return v.repo.ListVoucherRecordByFilter(ctx, filter)
}
