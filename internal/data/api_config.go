package data

import (
	"byd_wallet/internal/biz/coindata"
	"byd_wallet/internal/data/bijie"
	"byd_wallet/internal/data/covalenthq"
	"byd_wallet/internal/data/etherscan"
	"byd_wallet/internal/data/metapath"
	"byd_wallet/internal/data/okx"
	"byd_wallet/internal/data/weidubot"
	"byd_wallet/internal/thirdapi/btc528"
	"byd_wallet/internal/thirdapi/coingecko"
	"byd_wallet/model"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

func getAPIConfig(data *Data, api string) (*model.APIConfig, error) {
	r := &model.APIConfig{}
	if err := data.db.Model(&model.APIConfig{}).Where("api=?", api).Take(r).Error; err != nil {
		return nil, err
	}
	return r, nil
}

func NewCoinDataThirdAPI(data *Data) (coindata.CoinDataThirdAPI, error) {
	cfg, err := getAPIConfig(data, "coingecko")
	if err != nil {
		return nil, fmt.Errorf("get coingecko config fail: %w", err)
	}
	apiCfg := struct {
		ApiUrl string `json:"api_url"`
		ApiKey string `json:"api_key"`
	}{}
	err = json.Unmarshal(cfg.Config, &apiCfg)
	if err != nil {
		return nil, fmt.Errorf("parse coingecko config fail: %w", err)
	}
	return coingecko.NewCoingeckoClient(apiCfg.ApiUrl, apiCfg.ApiKey), nil
}

func NewCurrencyRateAPI(data *Data) (coindata.CurrencyRateAPI, error) {
	cfg, err := getAPIConfig(data, "btc528")
	if err != nil {
		return nil, fmt.Errorf("get btc528 config fail: %w", err)
	}
	apiCfg := struct {
		ApiUrl string `json:"api_url"`
		Sign   string `json:"sign"`
	}{}
	err = json.Unmarshal(cfg.Config, &apiCfg)
	if err != nil {
		return nil, fmt.Errorf("parse btc528 config fail: %w", err)
	}
	return btc528.NewBtc528Client(apiCfg.ApiUrl, apiCfg.Sign), nil
}

func NewOKXClient(data *Data, logger log.Logger) (*okx.Client, error) {
	cfg, err := getAPIConfig(data, "okx")
	if err != nil {
		return nil, fmt.Errorf("get okx config fail: %w", err)
	}
	apiCfg := &okx.Config{}
	err = json.Unmarshal(cfg.Config, apiCfg)
	if err != nil {
		return nil, fmt.Errorf("parse okx config fail: %w", err)
	}
	return okx.NewClient(apiCfg, logger)
}

func NewCovalenthqClient(data *Data, logger log.Logger) (*covalenthq.Client, error) {
	cfg, err := getAPIConfig(data, "covalenthq")
	if err != nil {
		return nil, fmt.Errorf("get covalenthq config fail: %w", err)
	}
	apiCfg := &covalenthq.Config{}
	err = json.Unmarshal(cfg.Config, apiCfg)
	if err != nil {
		return nil, fmt.Errorf("parse covalenthq config fail: %w", err)
	}
	return covalenthq.NewClient(apiCfg, logger), nil
}

func NewEtherscanClient(data *Data) (*etherscan.MultiChainClient, error) {
	cfg, err := getAPIConfig(data, "etherscan")
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return etherscan.NewMultiChainClient(nil), nil
		}
		return nil, fmt.Errorf("get etherscan config fail: %w", err)
	}
	apiCfg := &etherscan.Config{}
	err = json.Unmarshal(cfg.Config, apiCfg)
	if err != nil {
		return nil, fmt.Errorf("parse etherscan config fail: %w", err)
	}
	return etherscan.NewMultiChainClient(apiCfg), nil
}

func NewMetapathConfig(data *Data) (*metapath.Config, error) {
	cfg, err := getAPIConfig(data, "metapath")
	if err != nil {
		return nil, fmt.Errorf("get metapath config fail: %w", err)
	}
	apiCfg := &metapath.Config{}
	err = json.Unmarshal(cfg.Config, apiCfg)
	if err != nil {
		return nil, fmt.Errorf("parse metapath config fail: %w", err)
	}
	return apiCfg, nil
}

func NewWeidubotClient(logger log.Logger, data *Data) (*weidubot.Client, error) {
	cfg, err := getAPIConfig(data, "weidubot")
	if err != nil {
		return nil, fmt.Errorf("get weidubot config fail: %w", err)
	}
	apiCfg := &weidubot.ClientConf{}
	err = json.Unmarshal(cfg.Config, apiCfg)
	if err != nil {
		return nil, fmt.Errorf("parse weidubot config fail: %w", err)
	}
	return weidubot.NewClient(logger, apiCfg)
}

func NewBijieConfig(data *Data) (*bijie.Config, error) {
	cfg, err := getAPIConfig(data, "bijie")
	if err != nil {
		return nil, fmt.Errorf("get bijie config fail: %w", err)
	}
	apiCfg := &bijie.Config{}
	err = json.Unmarshal(cfg.Config, apiCfg)
	if err != nil {
		return nil, fmt.Errorf("parse bijie config fail: %w", err)
	}
	return apiCfg, nil
}
