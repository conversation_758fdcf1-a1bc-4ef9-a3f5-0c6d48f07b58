package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
)

func NewAppRPCEndpointRepo(data *Data) biz.AppRPCEndpointRepo {
	return &appConfigRepo{Data: data}
}

type appConfigRepo struct {
	*Data
}

func (a *appConfigRepo) ListAppRPCEndpoint(ctx context.Context) ([]*model.AppRPCEndpoint, error) {
	var list []*model.AppRPCEndpoint
	if err := a.DB(ctx).Model(&model.AppRPCEndpoint{}).Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}
