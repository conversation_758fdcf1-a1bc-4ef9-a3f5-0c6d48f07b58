package bijie

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"io"
	"net/http"
)

type Client struct {
	log    *log.Helper
	config *Config
}

func NewClient(logger log.Logger, config *Config) *Client {
	return &Client{log: log.NewHelper(logger), config: config}
}

// Config API配置
type Config struct {
	BaseURL      string `json:"base_url"`
	Debug        bool   `json:"debug"`
	ChainIndex   int64  `json:"chain_index"`
	TokenAddress string `json:"token_address"`
}

type BaseResponse struct {
	Code int64           `json:"code"`
	Msg  string          `json:"msg"`
	Data json.RawMessage `json:"data"`
}

func (c *Client) doPost(ctx context.Context, reqPath string, req any) (json.RawMessage, error) {
	reqURL := c.config.BaseURL + reqPath
	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to encode request body: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, http.MethodPost, reqURL, bytes.NewBuffer(body))
	if err != nil {
		return nil, fmt.Errorf("failed to create http request: %w", err)
	}
	headers := http.Header{}
	headers.Set("Content-Type", "application/json")
	headers.Set("accept", "application/json")
	httpReq.Header = headers

	// 创建 HTTP 客户端并配置代理
	cli := &http.Client{}

	resp, err := cli.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send http request: %w", err)
	}
	defer resp.Body.Close()

	respBodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}
	if c.config.Debug {
		c.log.Debugf("bijie api request: url: %s \n req: %s \n resp: %s", reqURL, body, respBodyBytes)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("bjjie API request failed with status code %d", resp.StatusCode)
	}

	var reply BaseResponse
	if err := json.Unmarshal(respBodyBytes, &reply); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}
	if reply.Code == 200 {
		return reply.Data, nil
	}
	return nil, fmt.Errorf("bjjie API request failed with code %d: %s", reply.Code, reply.Msg)
}
