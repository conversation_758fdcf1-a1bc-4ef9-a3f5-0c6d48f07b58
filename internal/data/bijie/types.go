package bijie

type VoucherStatus int

const (
	VoucherStatusUnused VoucherStatus = iota + 1
	VoucherStatusUsed
)

type GetVoucherByCodeRequest struct {
	Code     string `json:"code"`
	Nickname string `json:"nickname"`
}

type GetVoucherByCodeData struct {
	// 核销金额
	Amount float64 `json:"amount"`
	// 券码
	Code string `json:"code"`
	// 用户昵称
	Nickname string `json:"nickname"`
	// 1=已提现(生成券码),2=已核销(券码核销)
	Status VoucherStatus `json:"status"`
}

func (v GetVoucherByCodeData) IsUsed() bool {
	return v.Status == VoucherStatusUsed
}

type UseVoucherRequest struct {
	Code     string `json:"code"`
	Nickname string `json:"nickname"`
}
