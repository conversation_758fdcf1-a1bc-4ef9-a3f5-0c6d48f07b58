package bijie

import (
	"context"
	"encoding/json"
)

func (c *Client) GetVoucherByCode(ctx context.Context, req *GetVoucherByCodeRequest) (*GetVoucherByCodeData, error) {
	data, err := c.doPost(ctx, "/xhj-gather-app/open/promActivityMiningWithdrawal/external/getVerificationInfoByCode", req)
	if err != nil {
		return nil, err
	}
	var resp GetVoucherByCodeData
	if err = json.Unmarshal(data, &resp); err != nil {
		return nil, err
	}
	return &resp, nil
}

func (c *Client) UseVoucher(ctx context.Context, req *UseVoucherRequest) error {
	_, err := c.doPost(ctx, "/xhj-gather-app/open/promActivityMiningWithdrawal/external/verificationByCode", req)
	return err
}
