package bijie

import (
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestClient_GetVoucherByCode(t *testing.T) {
	cli := NewClient(log.DefaultLogger, &Config{
		Debug:        true,
		BaseURL:      "https://api-test.528btc.com.cn",
		ChainIndex:   20000714,
		TokenAddress: "******************************************",
	})
	data, err := cli.GetVoucherByCode(context.Background(), &GetVoucherByCodeRequest{
		Code:     "47709hha50aj1c6h2411h1b587785a06c5x103",
		Nickname: "bjw99919",
	})
	assert.NoError(t, err)
	fmt.Printf("%+v\n", data)
}

func TestClient_UseVoucher(t *testing.T) {
	cli := NewClient(log.DefaultLogger, &Config{
		Debug:        true,
		BaseURL:      "https://api-test.528btc.com.cn",
		ChainIndex:   20000714,
		TokenAddress: "******************************************",
	})
	err := cli.UseVoucher(context.Background(), &UseVoucherRequest{
		Code:     "47709hha50aj1c6h2411h1b587785a06c5x102",
		Nickname: "绝对盘感比特Y",
	})
	assert.NoError(t, err)
}
