package data

import (
	"byd_wallet/internal/biz/gaspool/paymaster/evm"
	"context"
	"fmt"
	"strconv"
)

type evmPaymasterRepo struct {
	*Data
	chainIndex int64 // 链索引，用于区分不同EVM链的数据
}

// NewEvmPaymasterRepo 创建EVM paymaster数据访问层实例
// 参数:
//   - data: 数据访问层基础实例
//   - chainIndex: 链索引，用于区分不同EVM链的数据
func NewEvmPaymasterRepo(data *Data, chainIndex int64) evm.Repo {
	return &evmPaymasterRepo{
		Data:       data,
		chainIndex: chainIndex,
	}
}

// NewEvmPaymasterRepoFactory 创建EVM paymaster repo工厂函数
// 用于为不同的链索引创建独立的repo实例，确保不同链的数据隔离
// 参数:
//   - data: 数据访问层基础实例
//
// 返回:
//   - evm.RepoFactory: repo工厂函数，接受chainIndex参数并返回对应链的repo实例
func NewEvmPaymasterRepoFactory(data *Data) evm.RepoFactory {
	return func(chainIndex int64) evm.Repo {
		return NewEvmPaymasterRepo(data, chainIndex)
	}
}

func (r *evmPaymasterRepo) keyWaitDepositTxTask() string {
	return fmt.Sprintf("evmpm:deposittx:%d", r.chainIndex)
}

// AllWaitDepositTxTask 获取所有EVM gas转账等待确认记录
// 从Redis哈希表中获取当前链的所有等待确认记录
func (r *evmPaymasterRepo) AllWaitDepositTxTask(ctx context.Context) ([]uint, error) {
	values, err := r.rd.SMembers(ctx, r.keyWaitDepositTxTask()).Result()
	if err != nil {
		return nil, err
	}
	ids := []uint{}
	for _, v := range values {
		id, err := strconv.Atoi(v)
		if err != nil {
			continue
		}
		ids = append(ids, uint(id))
	}
	return ids, nil
}
func (r *evmPaymasterRepo) AddWaitDepositTxTask(ctx context.Context, txID uint) error {
	idstr := strconv.Itoa(int(txID))
	return r.rd.SAdd(ctx, r.keyWaitDepositTxTask(), idstr).Err()
}

func (r *evmPaymasterRepo) RemoveWaitDepositTxTask(ctx context.Context, txID uint) error {
	idstr := strconv.Itoa(int(txID))
	return r.rd.SRem(ctx, r.keyWaitDepositTxTask(), idstr).Err()
}

func (r *evmPaymasterRepo) keyWaitGasTask() string {
	return fmt.Sprintf("evmpm:waitgas:%d", r.chainIndex)
}

func (r *evmPaymasterRepo) AddWaitGasTask(ctx context.Context, txID uint) error {
	idstr := strconv.Itoa(int(txID))
	return r.rd.SAdd(ctx, r.keyWaitGasTask(), idstr).Err()
}

func (r *evmPaymasterRepo) AllWaitGasTask(ctx context.Context) ([]uint, error) {
	values, err := r.rd.SMembers(ctx, r.keyWaitGasTask()).Result()
	if err != nil {
		return nil, err
	}
	ids := []uint{}
	for _, v := range values {
		id, err := strconv.Atoi(v)
		if err != nil {
			continue
		}
		ids = append(ids, uint(id))
	}
	return ids, nil
}

func (r *evmPaymasterRepo) RemoveWaitGasTask(ctx context.Context, txID uint) error {
	idstr := strconv.Itoa(int(txID))
	return r.rd.SRem(ctx, r.keyWaitGasTask(), idstr).Err()
}
