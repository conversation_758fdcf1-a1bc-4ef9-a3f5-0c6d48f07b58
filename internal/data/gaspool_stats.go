package data

import (
	"byd_wallet/internal/biz/gaspool/base"
	"context"
	"fmt"
	"strconv"

	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
)

// gasPoolStatsService gas pool使用统计服务实现
// 使用Redis存储用户的gas pool使用统计数据
type gasPoolStatsService struct {
	rd redis.UniversalClient // Redis客户端
}

// NewGasPoolStatsService 创建新的gas pool统计服务实例
// 参数:
//   - rd: Redis客户端
//
// 返回值:
//   - base.GasPoolStatsService: 统计服务接口实例
func NewGasPoolStatsService(rd redis.UniversalClient) base.GasPoolStatsService {
	return &gasPoolStatsService{
		rd: rd,
	}
}

// getStatsKey 生成用户统计数据的Redis键
// 参数:
//   - userID: 用户ID
//
// 返回值:
//   - string: Redis键名
func (s *gasPoolStatsService) getStatsKey(userID uint) string {
	return fmt.Sprintf("user:%d:stats", userID)
}

// RecordGasUsage 记录gas pool使用统计
// 功能说明：
// 1. 使用Redis HINCRBY命令原子性地增加使用次数
// 2. 使用Redis HINCRBYFLOAT命令原子性地累加消耗金额
// 3. 确保并发安全，避免数据竞争
// 参数:
//   - ctx: 上下文对象
//   - userID: 用户ID
//   - gasAmount: 消耗的gas数量(USDT，6位精度)
//
// 返回值:
//   - error: 错误信息
func (s *gasPoolStatsService) RecordGasUsage(ctx context.Context, userID uint, gasAmount decimal.Decimal) error {

	key := s.getStatsKey(userID)

	// 使用Redis管道批量执行操作，提高性能
	pipe := s.rd.Pipeline()

	// 增加使用次数
	pipe.HIncrBy(ctx, key, "usage_count", 1)

	// 累加消耗金额（转换为字符串以保持精度）
	gasAmountFloat, _ := gasAmount.Float64()
	pipe.HIncrByFloat(ctx, key, "total_consumed", gasAmountFloat)

	// 设置过期时间为1年，避免数据无限增长
	//pipe.Expire(ctx, key, 365*24*3600) // 1年

	// 执行管道操作
	_, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to record gas usage for user %d: %w", userID, err)
	}

	return nil
}

// GetUserStats 获取用户gas pool使用统计
// 功能说明：
// 1. 从Redis哈希表中获取用户的使用次数和累计消耗
// 2. 处理键不存在的情况，返回零值
// 3. 确保数据类型转换的安全性
// 参数:
//   - ctx: 上下文对象
//   - userID: 用户ID
//
// 返回值:
//   - usageCount: 使用次数
//   - totalConsumed: 累计消耗金额(USDT，6位精度)
//   - error: 错误信息
func (s *gasPoolStatsService) GetUserStats(ctx context.Context, userID uint) (usageCount int64, totalConsumed decimal.Decimal, err error) {
	key := s.getStatsKey(userID)

	// 使用HMGET批量获取多个字段，提高性能
	result, err := s.rd.HMGet(ctx, key, "usage_count", "total_consumed").Result()
	if err != nil {
		return 0, decimal.Zero, fmt.Errorf("failed to get user stats for user %d: %w", userID, err)
	}

	// 处理使用次数
	if result[0] != nil {
		if countStr, ok := result[0].(string); ok {
			usageCount, err = strconv.ParseInt(countStr, 10, 64)
			if err != nil {
				return 0, decimal.Zero, fmt.Errorf("failed to parse usage count for user %d: %w", userID, err)
			}
		}
	}

	// 处理累计消耗金额
	if result[1] != nil {
		if consumedStr, ok := result[1].(string); ok {
			totalConsumed, err = decimal.NewFromString(consumedStr)
			if err != nil {
				return 0, decimal.Zero, fmt.Errorf("failed to parse total consumed for user %d: %w", userID, err)
			}
		}
	}

	// 如果没有数据，返回零值
	if totalConsumed.IsZero() {
		totalConsumed = decimal.Zero
	}

	return usageCount, totalConsumed, nil
}
