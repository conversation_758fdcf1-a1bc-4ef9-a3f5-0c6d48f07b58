package data

import (
	"context"
	"testing"

	"github.com/go-redis/redismock/v9"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

// TestGasPoolStatsService_RecordGasUsage 测试记录gas pool使用统计
func TestGasPoolStatsService_RecordGasUsage(t *testing.T) {
	// 创建Redis mock客户端
	db, mock := redismock.NewClientMock()
	defer db.Close()

	// 创建统计服务实例
	service := NewGasPoolStatsService(db)

	ctx := context.Background()
	userID := uint(123)
	gasAmount := decimal.NewFromFloat(1.5) // 1.5 USDT

	// 设置Redis mock期望
	key := "user:123:stats"
	mock.ExpectHIncrBy(key, "usage_count", 1).SetVal(1)
	mock.ExpectHIncrByFloat(key, "total_consumed", 1.5).SetVal(1.5)
	mock.ExpectExpire(key, 365*24*3600).SetVal(true)

	// 执行测试
	err := service.RecordGasUsage(ctx, userID, gasAmount)

	// 验证结果
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

// TestGasPoolStatsService_RecordGasUsage_InvalidAmount 测试记录无效金额
func TestGasPoolStatsService_RecordGasUsage_InvalidAmount(t *testing.T) {
	// 创建Redis mock客户端
	db, _ := redismock.NewClientMock()
	defer db.Close()

	// 创建统计服务实例
	service := NewGasPoolStatsService(db)

	ctx := context.Background()
	userID := uint(123)
	gasAmount := decimal.NewFromFloat(-1.0) // 负数金额

	// 执行测试
	err := service.RecordGasUsage(ctx, userID, gasAmount)

	// 验证结果
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "gas amount must be positive")
}

// TestGasPoolStatsService_GetUserStats 测试获取用户统计
func TestGasPoolStatsService_GetUserStats(t *testing.T) {
	// 创建Redis mock客户端
	db, mock := redismock.NewClientMock()
	defer db.Close()

	// 创建统计服务实例
	service := NewGasPoolStatsService(db)

	ctx := context.Background()
	userID := uint(123)

	// 设置Redis mock期望
	key := "user:123:stats"
	mock.ExpectHMGet(key, "usage_count", "total_consumed").
		SetVal([]interface{}{"5", "10.5"})

	// 执行测试
	usageCount, totalConsumed, err := service.GetUserStats(ctx, userID)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, int64(5), usageCount)
	assert.Equal(t, decimal.NewFromFloat(10.5), totalConsumed)
	assert.NoError(t, mock.ExpectationsWereMet())
}

// TestGasPoolStatsService_GetUserStats_NoData 测试获取不存在用户的统计
func TestGasPoolStatsService_GetUserStats_NoData(t *testing.T) {
	// 创建Redis mock客户端
	db, mock := redismock.NewClientMock()
	defer db.Close()

	// 创建统计服务实例
	service := NewGasPoolStatsService(db)

	ctx := context.Background()
	userID := uint(999)

	// 设置Redis mock期望（返回nil值）
	key := "user:999:stats"
	mock.ExpectHMGet(key, "usage_count", "total_consumed").
		SetVal([]interface{}{nil, nil})

	// 执行测试
	usageCount, totalConsumed, err := service.GetUserStats(ctx, userID)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, int64(0), usageCount)
	assert.Equal(t, decimal.Zero, totalConsumed)
	assert.NoError(t, mock.ExpectationsWereMet())
}

// TestGasPoolStatsService_GetStatsKey 测试Redis键生成
func TestGasPoolStatsService_GetStatsKey(t *testing.T) {
	// 创建Redis mock客户端
	db, _ := redismock.NewClientMock()
	defer db.Close()

	// 创建统计服务实例
	service := &gasPoolStatsService{rd: db}

	// 测试不同的用户ID
	testCases := []struct {
		userID   uint
		expected string
	}{
		{123, "user:123:stats"},
		{1, "user:1:stats"},
		{999999, "user:999999:stats"},
	}

	for _, tc := range testCases {
		result := service.getStatsKey(tc.userID)
		assert.Equal(t, tc.expected, result)
	}
}
