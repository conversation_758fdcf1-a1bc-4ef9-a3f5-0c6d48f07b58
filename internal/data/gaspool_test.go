package data

import (
	"byd_wallet/model"
	"context"
	"fmt"
	"testing"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

type GasPoolRepoTestSuite struct {
	suite.Suite
	data *Data
	repo *gasPoolRepo
}

func TestGasPoolRepoTestSuite(t *testing.T) {
	suite.Run(t, new(GasPoolRepoTestSuite))
}

func (s *GasPoolRepoTestSuite) SetupSuite() {
	dsn := "host=127.0.0.1 user=root password=root dbname=wallet_test port=5432 sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if !s.NoError(err) {
		return
	}
	if !s.NoError(db.AutoMigrate(
		&model.GasPoolSponsorTx{},
	)) {
		return
	}
	rd := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})

	s.data = NewData(db, rd)
	s.repo = NewGasPoolRepo(s.data)
}

func (s *GasPoolRepoTestSuite) SetupTest() {
	for _, tb := range []string{
		(&model.GasPoolSponsorTx{}).TableName(),
	} {
		s.data.db.Exec(fmt.Sprintf("delete from public.%s", tb))
	}
	s.data.rd.FlushAll(context.Background())
}

func (s *GasPoolRepoTestSuite) TestUpdateGasPoolSponsorTx() {
	// init data
	stx := &model.GasPoolSponsorTx{
		ChainIndex: 1,
		TxHash:     "0x11",
		Status:     model.GasPoolTxStatusInit,
	}
	_, err := s.repo.SaveGasPoolSponsorTx(s.T().Context(), stx)
	s.NoError(err)
	stx = &model.GasPoolSponsorTx{
		ChainIndex: 1,
		TxHash:     "0x1222",
		Status:     model.GasPoolTxStatusInit,
	}
	stx, err = s.repo.SaveGasPoolSponsorTx(s.T().Context(), stx)
	s.NoError(err)

	// test
	update := &model.GasPoolSponsorTx{}
	update.ID = 0
	update.Status = model.GasPoolTxStatusPending
	err = s.repo.UpdateGasPoolSponsorTx(s.T().Context(), update)
	s.Error(err)

	update.ID = stx.ID
	err = s.repo.UpdateGasPoolSponsorTx(s.T().Context(), update)
	s.NoError(err)

	checkTx, err := s.repo.FindGasPoolSponsorTxByID(s.T().Context(), stx.ID)
	s.NoError(err)
	s.Equal(model.GasPoolTxStatusPending, checkTx.Status)
}
