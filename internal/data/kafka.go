package data

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	"byd_wallet/internal/conf"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/segmentio/kafka-go"
)

type eventPublisher struct {
	writers map[string]*kafka.Writer
}

func NewEventPublisher(c *conf.Data) (biz.EventPublisher, func(), error) {
	topics := []string{
		constant.TokenTopic,
		constant.HoldNewTokenTopic,
		constant.UserRegisterTopic,
		constant.VoucherTopic,
	}
	var cleanups []func()
	writers := make(map[string]*kafka.Writer)
	for _, topic := range topics {
		writer, cleanup, err := newKafkaWriter(c.Kafka.Broke<PERSON>, topic)
		if err != nil {
			for _, cf := range cleanups {
				cf()
			}
			return nil, nil, err
		}
		cleanups = append(cleanups, cleanup)
		writers[topic] = writer
	}
	return &eventPublisher{
			writers: writers,
		}, func() {
			for _, cf := range cleanups {
				cf()
			}
		}, nil
}

func (t *eventPublisher) Publish(ctx context.Context, event interface{}) error {
	var topic string
	switch event.(type) {
	case *biz.TxSyncEventTokenDeploy:
		topic = constant.TokenTopic
	case *biz.TxSyncEventHoldNewToken:
		topic = constant.HoldNewTokenTopic
	case *biz.UserRegisterEvent:
		topic = constant.UserRegisterTopic
	case *biz.VoucherRecord:
		topic = constant.VoucherTopic
	default:
		return fmt.Errorf("unknown event type: %v", event)
	}
	w, ok := t.writers[topic]
	if !ok {
		return fmt.Errorf("unknown topic: %s", topic)
	}

	ejson, err := json.Marshal(event)
	if err != nil {
		return err
	}
	message := kafka.Message{
		Key:   []byte(strconv.FormatInt(time.Now().Unix(), 10)),
		Value: ejson,
	}

	return w.WriteMessages(ctx, message)
}

func newKafkaWriter(brokers []string, topic string) (*kafka.Writer, func(), error) {
	if len(brokers) == 0 {
		return nil, nil, errors.New("no kafka brokers provided")
	}
	w := &kafka.Writer{
		Addr:     kafka.TCP(brokers...),
		Topic:    topic,
		Balancer: &kafka.LeastBytes{},
	}
	cleanup := func() {
		_ = w.Close()
	}
	if err := createTopicIfNotExists(brokers[0], topic); err != nil {
		cleanup()
		return nil, nil, fmt.Errorf("createTopicIfNotExists: %w", err)
	}
	return w, cleanup, nil
}

func createTopicIfNotExists(brokerAddr, topic string) error {
	if topic == "" {
		return nil
	}
	conn, err := kafka.Dial("tcp", brokerAddr)
	if err != nil {
		return err
	}
	defer conn.Close()

	ctrl, err := conn.Controller() // must be controller
	if err != nil {
		return err
	}

	ctrlConn, err := kafka.Dial("tcp", fmt.Sprintf("%s:%d", ctrl.Host, ctrl.Port))
	if err != nil {
		return err
	}
	defer ctrlConn.Close()

	topicConfigs := []kafka.TopicConfig{
		{
			Topic:             topic,
			NumPartitions:     3,
			ReplicationFactor: 1,
		},
	}

	// CreateTopics creates one topic per provided configuration with idempotent
	// operational semantics. In other words, if CreateTopics is invoked with a
	// configuration for an existing topic, it will have no effect.

	// CreateTopics = CreateTopicIfNotExists
	return ctrlConn.CreateTopics(topicConfigs...)
}
