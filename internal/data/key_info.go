package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"byd_wallet/utils"
	"context"
)

func NewKeyInfoRepo(data *Data) biz.KeyInfoRepo {
	return &keyInfoRepo{
		Data:   data,
		aesKey: []byte("GP~iXFn}wCvb8^eK"),
	}
}

type keyInfoRepo struct {
	*Data
	aesKey []byte
}

func (p *keyInfoRepo) CreateKeyInfo(ctx context.Context, ki *model.KeyInfo) error {
	key, err := p.encrypt([]byte(ki.PrivateKey))
	if err != nil {
		return err
	}
	ki.PrivateKey = key
	return p.DB(ctx).Create(ki).Error
}

func (p *keyInfoRepo) GetPrivateKey(ctx context.Context, address string) (string, error) {
	var ki model.KeyInfo
	if err := p.DB(ctx).Model(&model.KeyInfo{}).Where("address=?", address).Take(&ki).Error; err != nil {
		return "", err
	}
	keyBytes, err := p.decrypt(ki.<PERSON>Key)
	if err != nil {
		return "", err
	}
	return keyBytes, nil
}

func (p *keyInfoRepo) encrypt(origData []byte) (string, error) {
	return utils.EncryptWithAESCBC(origData, p.aesKey)
}

func (p *keyInfoRepo) decrypt(crypted string) (string, error) {
	plaintext, err := utils.DecryptWithAESCBC(crypted, p.aesKey)
	if err != nil {
		return "", err
	}
	return string(plaintext), nil
}
