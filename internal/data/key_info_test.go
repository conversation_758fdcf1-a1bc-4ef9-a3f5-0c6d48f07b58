package data

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"os"
	"path/filepath"
	"testing"
)

func newTestKeyInfoRepo(t *testing.T) biz.KeyInfoRepo {
	db, err := gorm.Open(sqlite.Open(filepath.Join(os.TempDir(), uuid.New().String()+"_key_info.db")), &gorm.Config{})
	assert.NoError(t, err)

	autoMigrateKeyInfo(t, db)

	dataInstance := &Data{db: db}
	return NewKeyInfoRepo(dataInstance)
}

func autoMigrateKeyInfo(t *testing.T, db *gorm.DB) {
	// AutoMigrate all relevant models
	err := db.AutoMigrate(
		&model.KeyInfo{},
	)
	assert.NoError(t, err)
}

func Test_keyInfoRepo(t *testing.T) {
	repo := newTestKeyInfoRepo(t)
	ctx := context.Background()
	err := repo.CreateKeyInfo(ctx, &model.KeyInfo{
		ChainIndex: constant.BscChainIndex,
		Address:    "******************************************",
		PrivateKey: "57304ae5d2ae0dc948380f67e3eaded2cc5a869e84ca89902a28271a6856d6ef",
	})
	assert.NoError(t, err)
	key, err := repo.GetPrivateKey(ctx, "******************************************")
	assert.NoError(t, err)
	assert.Equal(t, "57304ae5d2ae0dc948380f67e3eaded2cc5a869e84ca89902a28271a6856d6ef", key)
}
