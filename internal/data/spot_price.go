package data

import (
	"byd_wallet/internal/biz"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/adshao/go-binance/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
)

const spotPriceKey = "spot_prices"

type spotPriceRepo struct {
	*Data
	log *log.Helper

	pairs      []string
	token2pair map[string]string
	stopC      chan struct{}
}

func NewSpotPriceRepo(logger log.Logger, data *Data) (*spotPriceRepo, error) {
	cfg, err := getAPIConfig(data, "spot_price")
	if err != nil {
		return nil, fmt.Errorf("get spot price config fail: %w", err)
	}
	apiCfg := struct {
		Pairs      []string          `json:"pairs"`
		Token2Pair map[string]string `json:"token2pair"`
	}{}
	err = json.Unmarshal(cfg.Config, &apiCfg)
	if err != nil {
		return nil, fmt.Errorf("parse spot price config fail: %w", err)
	}
	token2Pair := make(map[string]string)
	for v, k := range apiCfg.Token2Pair {
		token2Pair[strings.ToLower(v)] = k
	}
	return &spotPriceRepo{
		Data:       data,
		log:        log.NewHelper(logger),
		pairs:      apiCfg.Pairs,
		token2pair: token2Pair,
	}, nil
}

func (repo *spotPriceRepo) GetTokenLatestPriceUSDT(ctx context.Context, chainIndex int64, address string) (price decimal.Decimal, timeUnix int64, err error) {
	sp, err := repo.findByAddress(ctx, chainIndex, address)
	if err != nil {
		return
	}
	price = sp.Price
	timeUnix = sp.Timestamp
	return
}

func (repo *spotPriceRepo) findByAddress(ctx context.Context, chainIndex int64, address string) (sp *biz.SpotPrice, err error) {
	pair, ok := repo.token2pair[fmt.Sprintf("%d%s", chainIndex, strings.ToLower(address))]
	if !ok {
		err = fmt.Errorf("token not found: %d: %s", chainIndex, address)
		return
	}
	if pair == "USDTUSDT" {
		sp = &biz.SpotPrice{
			TradingPair: pair,
			Price:       decimal.NewFromInt(1),
			Timestamp:   time.Now().Unix(),
		}
		return
	}
	sp, err = repo.FindByTradingPair(ctx, pair)
	if err != nil {
		return
	}
	return
}

func (repo *spotPriceRepo) FindByAddress(ctx context.Context, chainIndex int64, address string) (sp *biz.SpotPrice, err error) {
	return repo.findByAddress(ctx, chainIndex, address)
}

func (repo *spotPriceRepo) Subscribe(ctx context.Context) error {
	if repo.stopC != nil {
		repo.stopC = make(chan struct{})
	}

	wsHandler := func() (doneC, stopC chan struct{}, err error) {
		doneC, stopC, err = binance.WsCombinedMarketStatServe(repo.pairs, func(event *binance.WsMarketStatEvent) {
			price, err := decimal.NewFromString(event.LastPrice)
			if err != nil {
				return
			}
			err = repo.updatePrice(&biz.SpotPrice{
				TradingPair: event.Symbol,
				Price:       price,
				Timestamp:   event.Time / 1000,
			})
			if err != nil {
				repo.log.Errorf("update price error, %v", err)
			}
		}, func(err error) {
			repo.log.Errorf("WsCombinedMarketStatServe error, %v", err)
		})
		return
	}
	doneC, stopC, err := wsHandler()
	if err != nil {
		return err
	}

	go func() {
		repo.log.Info("start ws handler")
		retry := 1
		for {
			select {
			case <-repo.stopC:
				close(stopC)
			case <-doneC:
				time.Sleep(time.Duration(retry) * time.Second) // sleep before retry
				doneC, stopC, err = wsHandler()
				if err != nil {
					repo.log.Errorf("restart ws handler error, %v", err)
					retry += 1
					continue
				}
				retry = 1 // reset
			}
		}
	}()
	return nil
}

func (repo *spotPriceRepo) Unsubscribe(ctx context.Context) error {
	if repo.stopC == nil {
		return nil
	}
	close(repo.stopC)
	return nil
}

func (repo *spotPriceRepo) updatePrice(p *biz.SpotPrice) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	return repo.rd.HSet(ctx, spotPriceKey, p.TradingPair, p.MustJsonStr()).Err()
}

func (repo *spotPriceRepo) FindByTradingPair(ctx context.Context, tradingPair string) (*biz.SpotPrice, error) {
	bts, err := repo.rd.HGet(ctx, spotPriceKey, tradingPair).Bytes()
	if err != nil {
		return nil, err
	}
	p := &biz.SpotPrice{}
	err = json.Unmarshal(bts, p)
	if err != nil {
		return nil, err
	}
	return p, nil
}

func (repo *spotPriceRepo) ListByTradingPairs(ctx context.Context, tradingPairs []string) ([]*biz.SpotPrice, error) {
	ps := make([]*biz.SpotPrice, 0, len(tradingPairs))
	res, err := repo.rd.HMGet(ctx, spotPriceKey, tradingPairs...).Result()
	if err != nil {
		return nil, err
	}
	for _, v := range res {
		v, ok := v.(string)
		if !ok {
			continue
		}
		p := &biz.SpotPrice{}
		err := json.Unmarshal([]byte(v), p)
		if err != nil {
			return nil, err
		}
		ps = append(ps, p)
	}
	return ps, nil
}
