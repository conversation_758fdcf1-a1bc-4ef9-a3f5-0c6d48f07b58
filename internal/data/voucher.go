package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/data/bijie"
	"byd_wallet/model"
	"context"
	"github.com/shopspring/decimal"
)

func NewVoucherRepo(cli *bijie.Client, config *bijie.Config, repo biz.TokenAssetRepo) (biz.VoucherRepo, error) {
	token, err := repo.FindByChainIndexAndAddress(context.Background(), config.ChainIndex, config.TokenAddress)
	if err != nil {
		return nil, err
	}
	return &voucherRepo{
		Client: cli,
		token:  token,
	}, nil
}

type voucherRepo struct {
	*bijie.Client
	token *model.TokenAsset
}

func (v *voucherRepo) ListToken(ctx context.Context) ([]*model.TokenAsset, error) {
	return []*model.TokenAsset{v.token}, nil
}

func (v *voucherRepo) WriteOff(ctx context.Context, nickname, code string) error {
	return v.UseVoucher(ctx, &bijie.UseVoucherRequest{
		Code:     code,
		Nickname: nickname,
	})
}

func (v *voucherRepo) GetVoucher(ctx context.Context, nickname, code string) (*biz.Voucher, error) {
	data, err := v.GetVoucherByCode(ctx, &bijie.GetVoucherByCodeRequest{
		Code:     code,
		Nickname: nickname,
	})
	if err != nil {
		return nil, err
	}
	value := decimal.NewFromFloat(data.Amount).Mul(decimal.NewFromInt(10).Pow(decimal.NewFromInt(v.token.Decimals)))
	return &biz.Voucher{
		Value:      value.String(),
		TokenAsset: v.token,
		Used:       data.IsUsed(),
	}, nil
}
