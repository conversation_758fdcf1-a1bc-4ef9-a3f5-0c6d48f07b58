package data

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"time"
)

func NewVoucherRecordAdminRepo(data *Data) biz.VoucherRecordAdminRepo {
	return &voucherRecordAdminRepo{data}
}

type voucherRecordAdminRepo struct {
	*Data
}

func (v *voucherRecordAdminRepo) VoucherStats(ctx context.Context) ([]*biz.VoucherStats, error) {
	var stats []*biz.VoucherStats

	// 获取今日开始时间
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 按TokenAddress分组查询历史统计数据
	type StatResult struct {
		TokenAddress string
		Symbol       string
		Decimals     int64
		TotalValue   string
		TotalCount   int64
	}

	var totalStats []StatResult
	err := v.DB(ctx).Model(&model.VoucherRecord{}).
		Select("token_address, symbol, decimals, SUM(value) as total_value, COUNT(*) as total_count").
		Where("status = ?", model.VoucherStatusSuccess).
		Group("token_address, symbol, decimals").
		Scan(&totalStats).Error

	if err != nil {
		return nil, err
	}

	// 按TokenAddress分组查询今日统计数据
	type TodayStatResult struct {
		TokenAddress string
		ValueOfToday string
		CountOfToday int64
	}

	var todayStats []TodayStatResult
	err = v.DB(ctx).Model(&model.VoucherRecord{}).
		Select("token_address, SUM(value) as value_of_today, COUNT(*) as count_of_today").
		Where("status = ? AND created_at >= ?", model.VoucherStatusSuccess, todayStart).
		Group("token_address").
		Scan(&todayStats).Error

	if err != nil {
		return nil, err
	}

	// 创建今日数据的映射
	todayMap := make(map[string]TodayStatResult)
	for _, today := range todayStats {
		todayMap[today.TokenAddress] = today
	}

	// 合并数据
	for _, total := range totalStats {
		stat := &biz.VoucherStats{
			StatsSymbol:  total.Symbol,
			Decimals:     total.Decimals,
			TotalValue:   total.TotalValue,
			TotalCount:   total.TotalCount,
			ValueOfToday: "0",
			CountOfToday: 0,
		}

		// 如果有今日数据，则填充
		if todayData, exists := todayMap[total.TokenAddress]; exists {
			stat.ValueOfToday = todayData.ValueOfToday
			stat.CountOfToday = todayData.CountOfToday
		}

		stats = append(stats, stat)
	}

	return stats, nil
}

func (v *voucherRecordAdminRepo) ListVoucherRecordByFilter(ctx context.Context, filter biz.VoucherRecordAdminFilter) ([]*model.VoucherRecord, int64, error) {
	var records []*model.VoucherRecord
	var total int64

	query := v.DB(ctx).Model(&model.VoucherRecord{})

	if constant.IsValidChainIndex(filter.ChainIndex) {
		query = query.Where("chain_index = ?", filter.ChainIndex)
	}
	if filter.From != "" {
		query = query.Where("from_address = ?", filter.From)
	}
	if filter.To != "" {
		query = query.Where("to_address = ?", filter.To)
	}

	err := query.
		Scopes(Paginate(filter.Pagination)).
		Order("id DESC").
		Find(&records).Offset(-1).Count(&total).Error

	return records, total, err
}
