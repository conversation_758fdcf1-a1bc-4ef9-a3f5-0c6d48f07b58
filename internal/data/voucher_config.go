package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
)

func NewVoucherConfigRepo(data *Data) biz.VoucherConfigRepo {
	return &voucherConfigRepo{Data: data}
}

type voucherConfigRepo struct {
	*Data
}

func (v *voucherConfigRepo) GetVoucherConfig(ctx context.Context, chainIndex int64) (*model.VoucherConfig, error) {
	var config model.VoucherConfig
	if err := v.DB(ctx).Model(&model.VoucherConfig{}).Where("chain_index=?", chainIndex).First(&config).Error; err != nil {
		return nil, err
	}
	return &config, nil
}
