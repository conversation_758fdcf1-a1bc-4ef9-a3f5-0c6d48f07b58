package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/base"
	"byd_wallet/model"
	"context"
	"fmt"
	"time"
)

func NewVoucherRecordRepo(data *Data) biz.VoucherRecordRepo {
	return &voucherRecordRepo{Data: data}
}

type voucherRecordRepo struct {
	*Data
}

func (vr *voucherRecordRepo) PagedVoucherRecordsByAddresses(ctx context.Context, addresses []string, pagination base.Pagination) ([]*model.VoucherRecord, int64, error) {
	var records []*model.VoucherRecord
	var count int64
	if err := vr.DB(ctx).Scopes(Paginate(pagination)).
		Where("to_address in (?)", addresses).
		Preload("TokenAsset").
		Order("id desc").
		Find(&records).
		Offset(-1).Count(&count).
		Error; err != nil {
		return nil, 0, err
	}
	return records, count, nil
}

func (vr *voucherRecordRepo) ExistsVoucherRecord(ctx context.Context, code string) (bool, error) {
	var count int64
	if err := vr.DB(ctx).Model(&model.VoucherRecord{}).Where("code=?", code).Count(&count).Error; err != nil {
		return true, err
	}
	return count > 0, nil
}

func (vr *voucherRecordRepo) CreateVoucherRecord(ctx context.Context, v *model.VoucherRecord) error {
	return vr.DB(ctx).Create(v).Error
}

func (vr *voucherRecordRepo) UpdateVoucherRecordPending(ctx context.Context, id uint) error {
	db := vr.DB(ctx).Model(&model.VoucherRecord{}).
		Where("id=?", id).
		Where("status=?", model.VoucherStatusInit).
		Update("status", model.VoucherStatusPending)
	if db.Error != nil {
		return db.Error
	}
	if db.RowsAffected == 0 {
		return fmt.Errorf("update voucher record status to pending failed, id: %d", id)
	}
	return nil
}

func (vr *voucherRecordRepo) UpdateVoucherRecordSuccess(ctx context.Context, id uint, txAt *time.Time) error {
	db := vr.DB(ctx).Model(&model.VoucherRecord{}).
		Where("id=?", id).
		Where("status=?", model.VoucherStatusPending).
		Updates(map[string]any{
			"status":      model.VoucherStatusSuccess,
			"finished_at": txAt,
		})
	if db.Error != nil {
		return db.Error
	}
	if db.RowsAffected == 0 {
		return fmt.Errorf("update voucher record status to success failed, id: %d", id)
	}
	return nil
}

func (vr *voucherRecordRepo) UpdateVoucherRecordFail(ctx context.Context, id uint, errMsg string) error {
	now := time.Now()
	db := vr.DB(ctx).Model(&model.VoucherRecord{}).
		Where("id=?", id).
		Where("status=?", model.VoucherStatusPending).
		Updates(map[string]any{
			"status":      model.VoucherStatusFail,
			"err_msg":     errMsg,
			"finished_at": &now,
		})
	if db.Error != nil {
		return db.Error
	}
	if db.RowsAffected == 0 {
		return fmt.Errorf("update voucher record status to fail failed, id: %d", id)
	}
	return nil
}

func (vr *voucherRecordRepo) GetVoucherRecord(ctx context.Context, id uint) (*model.VoucherRecord, error) {
	var record model.VoucherRecord
	if err := vr.DB(ctx).Model(&model.VoucherRecord{}).
		Where("id=?", id).
		Preload("TokenAsset").
		Take(&record).Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (vr *voucherRecordRepo) UpdateVoucherRecordTxInfo(ctx context.Context, id uint, hash, method string) error {
	db := vr.DB(ctx).Model(&model.VoucherRecord{}).
		Where("id=?", id).
		Where("status=?", model.VoucherStatusPending).
		Updates(map[string]any{
			"tx_hash": hash,
			"method":  method,
		})
	if db.Error != nil {
		return db.Error
	}
	if db.RowsAffected == 0 {
		return fmt.Errorf("update voucher record tx info failed, id: %d", id)
	}
	return nil
}

func (vr *voucherRecordRepo) ListPendingVoucherRecords(ctx context.Context) ([]*model.VoucherRecord, error) {
	var records []*model.VoucherRecord
	if err := vr.DB(ctx).Model(&model.VoucherRecord{}).
		Where("status=?", model.VoucherStatusPending).
		Preload("TokenAsset").
		Find(&records).Error; err != nil {
		return nil, err
	}
	return records, nil
}
