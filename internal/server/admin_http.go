package server

import (
	v1 "byd_wallet/api/walletadmin/v1"
	"byd_wallet/internal/conf"
	"byd_wallet/internal/server/middleware/localize"
	"byd_wallet/internal/server/middleware/validate"
	"byd_wallet/internal/server/swagger"
	adminsrvi "byd_wallet/internal/service/admin"
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/selector"
	"github.com/go-kratos/kratos/v2/transport"
	"google.golang.org/grpc/codes"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/go-kratos/kratos/v2/transport/http/status"
)

type AdminHttpServer struct {
	*http.Server
}

func NewAdminHttpServer(c *conf.Server, logger log.Logger,
	admin *adminsrvi.AdminService,
	addr *adminsrvi.AddressService,
	user *adminsrvi.UserService,
	chain *adminsrvi.ChainService,
	coin *adminsrvi.CoinService,
	tx *adminsrvi.TxService,
	dapp *adminsrvi.DappService,
	file *adminsrvi.FileService,
	swap *adminsrvi.SwapService,
	appVersion *adminsrvi.AppVersionService,
	userGuide *adminsrvi.UserGuideService,
	gaspool *adminsrvi.GasPoolService,
	voucher *adminsrvi.VoucherService,
) *AdminHttpServer {
	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			AdminLoggingServer(logger),
			validate.ProtoValidate(),
			AdminJwtAuthMiddleware(admin.VerifyJwtToken,
				map[string]struct{}{
					v1.OperationAdminSrvLogin: {},
				},
				"admtoken",
			),
			localize.I18N(),
		),
		http.ResponseEncoder(ResponseEncoder),
		http.ErrorEncoder(ErrorEncoder),
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}
	srv := http.NewServer(opts...)
	v1.RegisterAdminSrvHTTPServer(srv, admin)
	v1.RegisterAddressSrvHTTPServer(srv, addr)
	v1.RegisterUserSrvHTTPServer(srv, user)
	v1.RegisterChainSrvHTTPServer(srv, chain)
	v1.RegisterCoinSrvHTTPServer(srv, coin)
	v1.RegisterTxSrvHTTPServer(srv, tx)
	v1.RegisterAdminDappSrvHTTPServer(srv, dapp)
	v1.RegisterFileSrvHTTPServer(srv, file)
	v1.RegisterSwapServiceHTTPServer(srv, swap)
	v1.RegisterAppVersionServiceHTTPServer(srv, appVersion)
	v1.RegisterUserGuideServiceHTTPServer(srv, userGuide)
	v1.RegisterGasPoolSrvHTTPServer(srv, gaspool)
	v1.RegisterVoucherServiceHTTPServer(srv, voucher)
	// 创建管理员API的现代化Swagger UI处理器，支持中文注释显示
	// 使用嵌入的OpenAPI文件确保生产环境可用，外部文件作为开发环境后备
	// Use embedded OpenAPI files for production reliability, external files as development fallback
	swaggerHandler := swagger.NewAdminHandler("/q")
	// 设置swagger-ui前缀，访问swagger-ui路由为/q/swagger-ui
	srv.HandlePrefix("/q/", swaggerHandler)
	return &AdminHttpServer{srv}
}

func AdminJwtAuthMiddleware(adminAuthFunc func(ctx context.Context, jwtToken string) (adminID uint, err error),
	skipAuthList map[string]struct{},
	jwtHeaderName string,
) middleware.Middleware {
	const reason string = "UNAUTHORIZED"
	var (
		ErrWrongContext = errors.Unauthorized(reason, "wrong context for middleware")
		ErrTokenInvalid = errors.Unauthorized(reason, "invalid auth")
	)

	return selector.Server(
		middleware.Middleware(func(h middleware.Handler) middleware.Handler {
			return func(ctx context.Context, req any) (any, error) {
				header, ok := transport.FromServerContext(ctx)
				if !ok {
					return nil, ErrWrongContext
				}
				jwtToken := header.RequestHeader().Get(jwtHeaderName)
				adminID, err := adminAuthFunc(ctx, jwtToken)
				if err != nil {
					return nil, ErrTokenInvalid
				}
				ctx = adminsrvi.NewAuthContext(ctx, adminID)
				return h(ctx, req)
			}
		}),
	).
		Match(func(_ context.Context, operation string) bool {
			_, ok := skipAuthList[operation]
			return !ok
		}).
		Build()
}

func AdminLoggingServer(logger log.Logger) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req any) (reply any, err error) {
			var (
				code      int32
				reason    string
				kind      string
				operation string
			)

			// default code
			code = int32(status.FromGRPCCode(codes.OK))

			startTime := time.Now()
			if info, ok := transport.FromServerContext(ctx); ok {
				kind = info.Kind().String()
				operation = info.Operation()
			}
			reply, err = handler(ctx, req)
			if se := errors.FromError(err); se != nil {
				code = se.Code
				reason = se.Reason
			}
			level, stack := extractError(err)
			var args string
			if operation != v1.OperationAdminSrvLogin {
				args = extractArgs(req)
			}

			extractArgs(req)
			log.NewHelper(log.WithContext(ctx, logger)).Log(level,
				"kind", "server",
				"component", kind,
				"operation", operation,
				"args", args,
				"code", code,
				"reason", reason,
				"stack", stack,
				"latency", time.Since(startTime).Seconds(),
			)
			return
		}
	}
}

// extractArgs returns the string of the req
func extractArgs(req any) string {
	if redacter, ok := req.(logging.Redacter); ok {
		return redacter.Redact()
	}
	if stringer, ok := req.(fmt.Stringer); ok {
		return stringer.String()
	}
	return fmt.Sprintf("%+v", req)
}

// extractError returns the string of the error
func extractError(err error) (log.Level, string) {
	if err != nil {
		return log.LevelError, fmt.Sprintf("%+v", err)
	}
	return log.LevelInfo, ""
}
