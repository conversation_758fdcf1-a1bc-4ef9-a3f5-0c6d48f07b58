package server

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/gaspool/paymaster/tron"
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

type GasPoolServer struct {
	log *log.Helper

	tasks map[string]AsyncTask
}

func NewGasPoolServer(
	logger log.Logger,
	tronPM *tron.Paymaster,
	evmPMs *biz.EVMPaymasters,
) *GasPoolServer {
	tasks := map[string]AsyncTask{
		"tron paymaster": tronPM,
	}

	// 添加所有EVM链的paymaster任务
	if evmPMs.Ethereum != nil {
		tasks["ethereum paymaster"] = evmPMs.Ethereum
	}
	if evmPMs.BSC != nil {
		tasks["bsc paymaster"] = evmPMs.BSC
	}
	if evmPMs.Polygon != nil {
		tasks["polygon paymaster"] = evmPMs.Polygon
	}
	if evmPMs.Arbitrum != nil {
		tasks["arbitrum paymaster"] = evmPMs.Arbitrum
	}
	if evmPMs.Optimism != nil {
		tasks["optimism paymaster"] = evmPMs.Optimism
	}
	if evmPMs.Base != nil {
		tasks["base paymaster"] = evmPMs.Base
	}

	return &GasPoolServer{
		log:   log.NewHelper(logger),
		tasks: tasks,
	}
}

func (s *GasPoolServer) Start(ctx context.Context) error {
	var cf func()
	for taskName, task := range s.tasks {
		s.log.Infof("async server start task: %s", taskName)
		if err := task.Start(ctx); err != nil {
			if cf != nil {
				cf()
			}
			return err
		}
		tmpCf := func() {
			task.Stop(ctx)
		}
		if cf == nil {
			cf = tmpCf
		} else {
			oldCf := cf
			cf = func() {
				oldCf()
				tmpCf()
			}
		}
	}
	return nil
}

func (s *GasPoolServer) Stop(ctx context.Context) error {
	for taskName, task := range s.tasks {
		s.log.Infof("async server stop task: %s", taskName)
		if err := task.Stop(ctx); err != nil {
			s.log.Errorf("async server stop task error: %v: taskName=%s", err, taskName)
		}
	}
	return nil
}
