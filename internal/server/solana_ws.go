package server

import (
	"byd_wallet/internal/service/task"
	"context"
	"log"
)

type SolanaWebSocketServer struct {
	service task.SolanaWebSocketService
}

func NewSolanaWebSocketServer(s task.SolanaWebSocketService) *SolanaWebSocketServer {
	return &SolanaWebSocketServer{
		service: s,
	}
}

func (s *SolanaWebSocketServer) Start(ctx context.Context) error {
	log.Println("Starting Solana WebSocket server...")
	if err := s.service.Start(ctx); err != nil {
		return err
	}
	log.Println("Solana WebSocket server started")
	return nil
}

func (s *SolanaWebSocketServer) Stop(ctx context.Context) error {
	log.Println("Stopping Solana WebSocket server...")
	if err := s.service.Stop(ctx); err != nil {
		return err
	}
	log.Println("Solana WebSocket server stopped")
	return nil
}
