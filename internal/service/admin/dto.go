package admin

import (
	v1 "byd_wallet/api/walletadmin/v1"
	"byd_wallet/common/constant"
	"byd_wallet/model"
)

func ToPbGasPoolDepositToken(v *model.GasPoolDepositTokenAdminView) *v1.GasPoolDepositToken {
	return &v1.GasPoolDepositToken{
		Name:             v.Name,
		Symbol:           v.Symbol,
		Decimals:         v.Decimals,
		LogoUrl:          v.LogoUrl,
		Address:          v.Address,
		ChainIndex:       v.ChainIndex,
		MinDepositAmount: v.MinDepositAmount.String(),
		Enable:           v.Enable,
		Id:               uint64(v.ID),
		ChainName:        constant.GetChainName(v.ChainIndex),
	}
}

func ToPbCoinStar(v *model.TokenAssetWithStar) *v1.CoinStar {
	return &v1.CoinStar{
		Id:        uint64(v.StarID),
		CreatedAt: v.StarCreatedAt.Unix(),
		SortOrder: v.SortOrder,

		Name:       v.Name,
		Symbol:     v.Symbol,
		ChainIndex: v.ChainIndex,
		Decimals:   v.Decimals,
		Address:    v.Address,
		ChainName:  constant.GetChainName(v.ChainIndex),
		LogoUrl:    v.LogoUrl,
	}
}

func ToPbAddress(v *model.UserAddress) *v1.Address {
	return &v1.Address{
		Id:         uint64(v.ID),
		BossId:     v.User.Username,
		ChainIndex: v.ChainIndex,
		Address:    v.Address,
		CreatedAt:  v.CreatedAt.Unix(),
		ChainName:  constant.GetChainName(v.ChainIndex),
	}
}

func ToPbUser(v *model.User) *v1.User {
	return &v1.User{
		Id:        uint64(v.ID),
		BossId:    v.Username,
		CreatedAt: v.CreatedAt.Unix(),
		Enable:    v.Enable,
	}
}

func ToPbChain(v *model.BlockchainNetwork) *v1.Chain {
	return &v1.Chain{
		Id:            uint64(v.ID),
		ChainName:     v.ChainName,
		ChainIndex:    v.ChainIndex,
		Symbol:        v.Symbol,
		Decimals:      v.Decimals,
		ChainType:     v.ChainType,
		CurrencyBlock: v.CurrentBlock,
		IsSyncing:     v.IsSyncing,
		IsDisplay:     v.IsDisplay,
		BlockchainUrl: v.BlockchainURL,
		TokenUrl:      v.TokenURL,
		ChainId:       v.ChainID,
		SortOrder:     v.SortOrder,
	}
}

func ToPbCoin(v *model.TokenAsset) *v1.Coin {
	return &v1.Coin{
		Id:         uint64(v.ID),
		Name:       v.Name,
		Symbol:     v.Symbol,
		ChainIndex: v.ChainIndex,
		Decimals:   v.Decimals,
		Address:    v.Address,
		CreatedAt:  v.CreatedAt.Unix(),
		ChainName:  constant.GetChainName(v.ChainIndex),
		ChainId:    v.ChainId,
		LogoUrl:    v.LogoUrl,
		IsDisplay:  v.IsDisplay,
	}
}

func ToPbTxView(v *model.TransactionView) *v1.Tx {
	return &v1.Tx{
		Id:           uint64(v.ID),
		Name:         v.TokenName,
		Symbol:       v.TokenSymbol,
		ChainIndex:   v.ChainIndex,
		Decimals:     v.TokenDecimals,
		TokenAddress: v.ProgramID,
		TxTime:       v.Timestamp,
		ChainName:    constant.GetChainName(v.ChainIndex),
		FromAddress:  v.FromAddress,
		ToAddress:    v.ToAddress,
		Status:       v.Status,
		Value:        v.Value,
		Fee:          v.Fee,
		Method:       v.Method,
	}
}
