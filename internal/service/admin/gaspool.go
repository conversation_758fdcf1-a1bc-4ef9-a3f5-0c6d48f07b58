package admin

import (
	"byd_wallet/api/walletadmin/base"
	v1 "byd_wallet/api/walletadmin/v1"
	"byd_wallet/common/dto"
	"byd_wallet/internal/biz/gaspool"
	"context"
	"fmt"

	"google.golang.org/protobuf/types/known/emptypb"
)

type GasPoolService struct {
	v1.UnimplementedGasPoolSrvServer
	uc *gaspool.Usecase
}

func NewGasPoolService(uc *gaspool.Usecase) *GasPoolService {
	return &GasPoolService{uc: uc}
}

func (s *GasPoolService) ListGasPoolDepositToken(ctx context.Context, req *v1.ListGasPoolDepositTokenReq) (*v1.ListGasPoolDepositTokenReply, error) {
	list, err := s.uc.ListGasPoolDepositTokenAdminView(ctx)
	if err != nil {
		return nil, err
	}
	return &v1.ListGasPoolDepositTokenReply{
		List: dto.ToList(list, ToPbGasPoolDepositToken),
	}, nil
}
func (s *GasPoolService) UpdateGasPoolDepositToken(ctx context.Context, req *v1.UpdateGasPoolDepositTokenReq) (*emptypb.Empty, error) {
	var enable bool
	switch req.Enable {
	case base.EnableStatus_ENABLE_STATUS_ENABLE:
		enable = true
	case base.EnableStatus_ENABLE_STATUS_DISABLE:
		enable = false
	default:
		return nil, fmt.Errorf("invalid enable status: %d", req.Enable)
	}

	err := s.uc.UpdateGasPoolDepositToken(ctx, uint(req.Id), map[string]interface{}{
		"enable": enable,
	})
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}
