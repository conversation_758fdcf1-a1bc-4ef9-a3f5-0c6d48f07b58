package admin

import (
	pb "byd_wallet/api/walletadmin/v1"
	"byd_wallet/common/dto"
	"byd_wallet/internal/biz"
	"context"
)

var _ pb.VoucherServiceServer = (*VoucherService)(nil)

func NewVoucherService(uc *biz.VoucherAdminUsecase) *VoucherService {
	return &VoucherService{uc: uc}
}

type VoucherService struct {
	pb.UnimplementedVoucherServiceServer
	uc *biz.VoucherAdminUsecase
}

func (v *VoucherService) VoucherStats(ctx context.Context, req *pb.VoucherStatsReq) (*pb.VoucherStatsReply, error) {
	data, err := v.uc.VoucherStats(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.VoucherStatsReply{
		StatsSymbol:  data.StatsSymbol,
		Decimals:     data.Decimals,
		ValueOfToday: data.ValueOfToday,
		CountOfToday: data.CountOfToday,
		TotalValue:   data.TotalValue,
		TotalCount:   data.TotalCount,
	}, nil
}

func (v *VoucherService) ListVoucherRecord(ctx context.Context, req *pb.ListVoucherRecordReq) (*pb.ListVoucherRecordReply, error) {
	list, count, err := v.uc.ListVoucherRecordByFilter(ctx, biz.VoucherRecordAdminFilter{
		ChainIndex: req.ChainIndex,
		From:       req.FromAddress,
		To:         req.ToAddress,
		Pagination: FromPagination(req.Page, req.PageSize),
	})
	if err != nil {
		return nil, err
	}
	return &pb.ListVoucherRecordReply{
		List:       dto.ToList(list, FromVoucherRecord),
		TotalCount: count,
	}, nil
}
