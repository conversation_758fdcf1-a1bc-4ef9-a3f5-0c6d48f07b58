package admin

import (
	pb "byd_wallet/api/walletadmin/v1"
	"byd_wallet/model"
)

func FromVoucherRecord(from *model.VoucherRecord) *pb.VoucherRecord {
	return &pb.VoucherRecord{
		Id:           uint64(from.ID),
		ChainName:    from.ChainName,
		Symbol:       from.Symbol,
		TokenAddress: from.TokenAddress,
		Value:        from.Value,
		FromAddress:  from.FromAddress,
		ToAddress:    from.ToAddress,
		Timestamp:    from.Timestamp(),
		Status:       string(from.Status),
		Decimals:     from.Decimals,
		ChainIndex:   from.ChainIndex,
	}
}
