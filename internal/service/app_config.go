package service

import (
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/internal/biz"
	"context"
)

var _ v1.AppConfigServiceServer = (*AppConfigService)(nil)

type AppConfigService struct {
	v1.UnimplementedAppConfigServiceServer
	uc *biz.AppConfigUsecase
}

func NewAppConfigService(uc *biz.AppConfigUsecase) *AppConfigService {
	return &AppConfigService{uc: uc}
}

func (a AppConfigService) GetAppConfig(ctx context.Context, req *v1.GetAppConfigReq) (*v1.GetAppConfigReply, error) {
	data, err := a.uc.GetAppConfig(ctx)
	if err != nil {
		return nil, err
	}
	return &v1.GetAppConfigReply{
		Endpoints: data.EncryptedEndpoints,
	}, nil
}
