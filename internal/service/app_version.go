package service

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/service/admin"
	"context"

	v1 "byd_wallet/api/wallet/v1"
	adminV1 "byd_wallet/api/walletadmin/v1"
)

var _ v1.AppVersionServiceServer = (*AppVersionService)(nil)

func NewAppVersionService(uc *biz.AppVersionUsecase) *AppVersionService {
	return &AppVersionService{uc: uc}
}

// AppVersionService app版本管理服务
type AppVersionService struct {
	v1.UnimplementedAppVersionServiceServer
	uc *biz.AppVersionUsecase
}

func (a *AppVersionService) GetLatestAppVersion(ctx context.Context, req *v1.GetLatestAppVersionReq) (*adminV1.AppVersionInfo, error) {
	data, err := a.uc.GetLatestAppVersion(ctx, req.AppType)
	if err != nil {
		return nil, err
	}
	return admin.FromAppVersion(data), nil
}
