package service

import (
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"github.com/jinzhu/copier"
)

func ToTokenApprovals(froms []*biz.TokenApproval) []*v1.TokenApproval {
	out := make([]*v1.TokenApproval, len(froms))
	for i, from := range froms {
		out[i] = ToTokenApproval(from)
	}
	return out
}

func ToTokenApproval(from *biz.TokenApproval) *v1.TokenApproval {
	return &v1.TokenApproval{
		Token:    ToPbToken(from.Token),
		Approval: ToApproval(from.Approval),
	}
}

func ToDappBlockchainNetworks(froms []*biz.UserApprovedDapp) []*v1.UserApprovedDapp {
	out := make([]*v1.UserApprovedDapp, len(froms))
	for i, from := range froms {
		out[i] = ToUserApprovedDapp(from)
	}
	return out
}

func ToUserApprovedDapp(from *biz.UserApprovedDapp) *v1.UserApprovedDapp {
	return &v1.UserApprovedDapp{
		Addresses: from.Addresses,
		Dapp:      ToDapp(from.Dapp),
		Network:   ToBlockchainNetwork(from.BlockchainNetwork),
		Spender:   from.Spender,
	}
}

func FromUserAddresses(froms []*v1.UserAddress) []model.UserAddress {
	out := make([]model.UserAddress, len(froms))
	for i, from := range froms {
		out[i] = FromUserAddress(from)
	}
	return out
}

func ToUserAddresses(froms []model.UserAddress) []*v1.UserAddress {
	out := make([]*v1.UserAddress, len(froms))
	for i, from := range froms {
		out[i] = ToUserAddress(&from)
	}
	return out
}

func ToTokenDappApprovals(from *biz.TokenDappApprovals) *v1.ListApprovalByUserAddressReply {
	return &v1.ListApprovalByUserAddressReply{
		Address: ToUserAddress(&from.Address),
		Network: ToBlockchainNetwork(from.Network),
		List:    ToTokenDappApprovalSlice(from.TokenDappApproval),
	}
}

func ToTokenDappApprovalSlice(froms []*biz.TokenDappApproval) []*v1.TokenDappApproval {
	out := make([]*v1.TokenDappApproval, len(froms))
	for i, from := range froms {
		out[i] = ToTokenDappApproval(from)
	}
	return out
}

func ToTokenDappApproval(from *biz.TokenDappApproval) *v1.TokenDappApproval {
	return &v1.TokenDappApproval{
		Token:    ToPbToken(from.Token),
		Dapp:     ToDapp(from.Dapp),
		Approval: ToApproval(from.Approval),
	}
}

func ToApproval(from *model.Approval) *v1.Approval {
	return &v1.Approval{
		ChainIndex:     from.ChainIndex,
		OwnerAddress:   from.OwnerAddress,
		SpenderAddress: from.SpenderAddress,
		TokenAddress:   from.TokenAddress,
		Value:          from.Value,
	}
}

func ToUserAddress(from *model.UserAddress) *v1.UserAddress {
	return &v1.UserAddress{
		Address:    from.Address,
		ChainIndex: from.ChainIndex,
	}
}

func FromUserAddress(from *v1.UserAddress) model.UserAddress {
	return model.UserAddress{
		Address:    from.Address,
		ChainIndex: from.ChainIndex,
	}
}

func ToDapps(froms []*model.Dapp) []*v1.Dapp {
	out := make([]*v1.Dapp, len(froms))
	for i, from := range froms {
		out[i] = ToDapp(from)
	}
	return out
}

func ToDapp(from *model.Dapp) *v1.Dapp {
	if from == nil {
		return nil
	}
	var out v1.Dapp
	_ = copier.Copy(&out, from)
	out.Id = uint64(from.ID)
	out.I18Ns = ToDappI18Ns(from.DappI18Ns)
	var networks []*v1.BlockchainNetwork
	for _, network := range from.DappBlockchainNetworks {
		if network == nil || network.BlockchainNetwork == nil {
			continue
		}
		networks = append(networks, ToBlockchainNetwork(network.BlockchainNetwork))
	}
	out.Networks = networks
	return &out
}

func ToBlockchainNetwork(from *model.BlockchainNetwork) *v1.BlockchainNetwork {
	if from == nil {
		return nil
	}
	return &v1.BlockchainNetwork{
		ChainId: from.ChainID,
	}
}

func ToDappI18Ns(froms []*model.DappI18N) []*v1.DappI18N {
	var out []*v1.DappI18N
	_ = copier.Copy(&out, froms)
	return out
}

func ToDappCategory(from *model.DappCategory) *v1.DappCategory {
	if len(from.DappCategoryI18Ns) == 0 {
		return nil
	}
	if len(from.DappCategoryRels) == 0 {
		return nil
	}
	dci := from.DappCategoryI18Ns[0]
	var dapps []*v1.Dapp
	for _, rel := range from.DappCategoryRels {
		if rel.Dapp == nil {
			continue
		}
		rel.Dapp.CreatedAt = rel.CreatedAt
		fromDapp := rel.Dapp
		dapps = append(dapps, ToDapp(fromDapp))
	}
	if len(dapps) == 0 {
		return nil
	}
	return &v1.DappCategory{
		Id:       uint64(from.ID),
		Name:     dci.Name,
		Summary:  dci.Summary,
		Language: dci.Language,
		Dapps:    dapps,
	}
}

func ToDappTopic(from *model.DappTopic) *v1.DappTopic {
	if len(from.DappTopicI18Ns) == 0 {
		return nil
	}
	if len(from.DappTopicRels) == 0 {
		return nil
	}
	dti := from.DappTopicI18Ns[0]
	var dapps []*v1.Dapp
	for _, rel := range from.DappTopicRels {
		if rel.Dapp == nil {
			continue
		}
		fromDapp := rel.Dapp
		dapps = append(dapps, ToDapp(fromDapp))
	}
	return &v1.DappTopic{
		Id:            uint64(from.ID),
		BackgroundUrl: from.BackgroundUrl,
		Name:          dti.Name,
		Summary:       dti.Summary,
		Language:      dti.Language,
		Title:         dti.Title,
		TopTitle:      dti.TopTitle,
		BottomTitle:   dti.BottomTitle,
		Dapps:         dapps,
	}
}
