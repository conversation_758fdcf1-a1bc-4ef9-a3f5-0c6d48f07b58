package service

import (
	"byd_wallet/internal/service/admin"
	"byd_wallet/internal/service/mq"
	"byd_wallet/internal/service/task"

	"github.com/google/wire"
)

// ProviderSet is service providers.
var ProviderSet = wire.NewSet(
	task.NewEthHandler,
	task.NewSolHandler,
	task.NewTokenProcessor,

	NewUserService,
	NewTokenService,
	NewWalletSrvService,
	NewMarketService,
	NewTronService,
	NewDappService,
	NewSwapService,
	NewGasPoolService,
	NewAppVersionService,
	NewUserGuideService,
	NewVoucherService,
	NewAppConfigService,

	admin.NewAdminService,
	admin.NewAddressService,
	admin.NewUserService,
	admin.NewChainService,
	admin.NewCoinService,
	admin.NewTxService,
	admin.NewDappService,
	admin.NewFileService,
	admin.NewSwapService,
	admin.NewAppVersionService,
	admin.NewUserGuideService,
	admin.NewGasPoolService,
	admin.NewVoucherService,

	task.NewSolanaWebSocketService,
	task.NewSolanaCronSyncService,

	mq.NewTokenAssetService,
	mq.NewUserHoldTokenService,
	mq.NewUserRegisterService,
	mq.NewVoucherService,
)
