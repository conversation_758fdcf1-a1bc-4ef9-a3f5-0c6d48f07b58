package service

import (
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/common/dto"
	"byd_wallet/internal/biz"
	"context"
)

var _ v1.VoucherSrvServer = (*VoucherService)(nil)

type VoucherService struct {
	v1.UnimplementedVoucherSrvServer
	uc *biz.VoucherUsecase
}

func (v *VoucherService) GetVoucher(ctx context.Context, req *v1.GetVoucherReq) (*v1.GetVoucherReply, error) {
	data, err := v.uc.GetVoucher(ctx, req.GetNickname(), req.GetCode())
	if err != nil {
		return nil, v1.ErrorVoucherMatch(err.Error())
	}
	status := int64(1)
	if data.Used {
		status = 2
	}
	return &v1.GetVoucherReply{
		Data: &v1.Voucher{
			Value:  data.Value,
			Status: status,
			Token:  ToPbToken(data.TokenAsset),
		},
	}, nil
}

func (v *VoucherService) ListToken(ctx context.Context, _ *v1.ListVoucherTokenReq) (*v1.ListVoucherTokenReply, error) {
	list, err := v.uc.ListToken(ctx)
	if err != nil {
		return nil, err
	}
	return &v1.ListVoucherTokenReply{
		List: dto.ToList(list, ToPbToken),
	}, nil
}

func NewVoucherService(uc *biz.VoucherUsecase) *VoucherService {
	return &VoucherService{uc: uc}
}

func (v *VoucherService) Redeem(ctx context.Context, req *v1.RedeemReq) (*v1.RedeemReply, error) {
	if err := v.uc.RedeemVoucher(ctx, &biz.RedeemInput{
		Code:       req.Code,
		Nickname:   req.Nickname,
		ChainIndex: req.ChainIndex,
		Address:    req.Address,
	}); err != nil {
		return nil, v1.ErrorVoucherRedeem(err.Error())
	}
	return &v1.RedeemReply{}, nil
}

func (v *VoucherService) ListVoucherRecord(ctx context.Context, req *v1.ListVoucherRecordReq) (*v1.ListVoucherRecordReply, error) {
	list, count, err := v.uc.PagedVoucherRecordsByAddresses(ctx, req.Addresses, FromPagination(req.Page, req.Limit))
	if err != nil {
		return nil, err
	}
	return &v1.ListVoucherRecordReply{
		List:  dto.ToList(list, ToVoucherRecord),
		Count: count,
	}, nil
}
