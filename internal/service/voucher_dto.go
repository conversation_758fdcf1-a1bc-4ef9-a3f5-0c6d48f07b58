package service

import (
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/model"
)

func ToVoucherRecord(from *model.VoucherRecord) *v1.VoucherRecord {
	status := string(from.Status)
	if from.Status == model.VoucherStatusInit {
		status = string(model.VoucherStatusPending)
	}
	return &v1.VoucherRecord{
		Id:            uint64(from.ID),
		VoucherValue:  from.VoucherValue,
		VoucherSymbol: from.TokenAsset.Symbol,
		Value:         from.Value,
		Symbol:        from.TokenAsset.Symbol,
		ChainIndex:    from.TokenAsset.ChainIndex,
		Address:       from.ToAddress,
		Status:        status,
		Timestamp:     from.Timestamp(),
		Decimals:      from.TokenAsset.Decimals,
	}
}
