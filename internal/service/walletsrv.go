package service

import (
	pb "byd_wallet/api/wallet/v1"
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/common/constant"
	"byd_wallet/common/dto"
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/internal/biz/gaspool/paymaster/tron"
	"byd_wallet/model"
	"context"
	"errors"
	"fmt"

	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/types/known/emptypb"
	"gorm.io/gorm"
)

type WalletSrvService struct {
	pb.UnimplementedWalletSrvServer
	uc   *biz.WalletUsecase
	taUc *biz.TokenAssetUsecase
	bnUc *biz.BlockchainNetworkUsecase
	uht  *biz.UserHoldTokenUsecase
	txUc *biz.TransactionUsecase
	gpUc *gaspool.Usecase
}

func NewWalletSrvService(uc *biz.WalletUsecase,
	taUc *biz.TokenAssetUsecase,
	bnUc *biz.BlockchainNetworkUsecase,
	uht *biz.UserHoldTokenUsecase,
	txUc *biz.TransactionUsecase,
	gpUc *gaspool.Usecase) *WalletSrvService {
	return &WalletSrvService{
		uc:   uc,
		taUc: taUc,
		bnUc: bnUc,
		uht:  uht,
		txUc: txUc,
		gpUc: gpUc,
	}
}

func (s *WalletSrvService) QueryTxLatestToken(ctx context.Context, req *v1.QueryTxLatestTokenReq) (*v1.QueryTxLatestTokenReply, error) {
	list, err := s.uht.ListUserHoldTokenView(ctx, ToBizUserHoldTokenFilter(req))
	if err != nil {
		return nil, err
	}
	return &v1.QueryTxLatestTokenReply{
		List: dto.ToList(list, ToPbTokenWithWallet),
	}, nil
}

func (s *WalletSrvService) ListTokenBalance(ctx context.Context, req *v1.ListTokenBalanceReq) (*v1.ListTokenBalanceReply, error) {
	list := make([]*v1.WalletBalance, 0, len(req.Addresses))

	for _, v := range req.GetAddresses() {
		tbs, err := s.taUc.ListTokenBalanceByAddress(ctx, v.GetChainIndexes(), v.GetAddress())
		if err != nil {
			return nil, err
		}
		list = append(list, &v1.WalletBalance{
			Address:  v.GetAddress(),
			Balances: tbs,
		})
	}

	return &v1.ListTokenBalanceReply{
		List: list,
	}, nil
}

func (s *WalletSrvService) GetToken(ctx context.Context, req *pb.GetTokenReq) (*pb.Token, error) {
	ta, err := s.taUc.FindTokenAssetByChainIndexAndAddress(ctx, req.ChainIndex, req.Address)
	if err != nil {
		return nil, err
	}
	return ToPbToken(ta), nil
}

func (s *WalletSrvService) NetworkList(ctx context.Context, req *pb.NetworkListReq) (*pb.NetworkListReply, error) {
	list, err := s.bnUc.AllBlockchainNetwork(ctx)
	if err != nil {
		return nil, err
	}
	var networks []*pb.Network
	for _, v := range list {
		var network pb.Network
		network.Name = v.Name
		network.ChainIndex = v.ChainIndex
		network.Symbol = v.Symbol
		network.Decimals = v.Decimals
		network.BlockchainUrl = v.BlockchainURL
		network.TokenUrl = v.TokenURL
		network.ExplorerUrl = v.ExplorerURL
		network.Handle = v.Handle
		network.ChainType = v.ChainType
		network.GasTokenSymbol = v.GasTokenSymbol
		network.ChainId = v.ChainID
		network.SortOrder = v.SortOrder
		network.ChainName = v.ChainName
		networks = append(networks, &network)
	}

	return &pb.NetworkListReply{
		List: networks,
	}, nil
}

func (s *WalletSrvService) Transactions(ctx context.Context, req *pb.TransactionsReq) (*pb.TransactionsReply, error) {
	list, total, err := s.uc.TransactionList(req)
	if err != nil {
		return nil, err
	}
	var transactions []*pb.Transaction
	for _, v := range list {
		var transaction pb.Transaction
		transaction.Txn = v.TxHash
		transaction.ChainIndex = v.ChainIndex
		transaction.FromAddress = v.FromAddress
		transaction.ToAddress = v.ToAddress
		transaction.Value = v.Value
		transaction.ProgramId = v.ProgramID
		transaction.Fee = v.Fee
		transaction.Timestamp = v.Timestamp
		transaction.Method = v.Method
		transaction.Status = v.Status
		transaction.BlockNumber = v.BlockNumber
		transactions = append(transactions, &transaction)
	}
	return &pb.TransactionsReply{
		List:  transactions,
		Count: total,
	}, nil
}

func (s *WalletSrvService) TokenList(ctx context.Context, req *pb.TokenListReq) (*pb.TokenListReply, error) {
	list, totalCount, err := s.taUc.ListTokenAssetView(ctx, &biz.TokenAssetViewFilter{
		Page:         req.Page,
		PageSize:     req.Limit,
		ChainIndexes: dto.ParseChainIndexes(req.ChainIndexes),
		Search:       req.Query,
	})
	if err != nil {
		return nil, err
	}
	return &pb.TokenListReply{
		List:  dto.ToList(list, ToPbToken),
		Count: totalCount,
	}, nil
}

func (s *WalletSrvService) TransactionInfo(ctx context.Context, req *pb.TransactionReq) (*pb.TransactionDetail, error) {
	tx, err := s.txUc.FindTransactionByTxHash(ctx, req.GetChainIndex(), req.GetTxn())
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
	}
	txDetail := &pb.TransactionDetail{}
	stx, err := s.gpUc.FindGasPoolSponsorTxByTxHash(ctx, req.GetChainIndex(), req.GetTxn())
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
	}
	if stx != nil &&
		stx.TxType.IsUseGasPool() {
		// gas pool record
		txDetail.UseGaspool = true
		status := "pending"
		gpIsSuccess := false
		switch stx.Status {
		case model.GasPoolTxStatusSuccess:
			status = "success"
			gpIsSuccess = true
		case model.GasPoolTxStatusFail:
			status = "fail"
		}
		energyTrx, energyUSDT := decimal.Zero, decimal.Zero
		if stx.Energy.IsPositive() {
			energyTrx = stx.Energy.Mul(stx.EnergyPrice)
			energyUSDT = energyTrx.Div(tron.DecimalsTRX).Mul(stx.Price).Mul(tron.DecimalsUSDT).RoundUp(0)
		}
		bandwidthTrx, bandwidthUSDT := decimal.Zero, decimal.Zero
		if stx.Bandwidth.IsPositive() {
			bandwidthTrx = stx.Bandwidth.Mul(stx.BandwidthPrice)
			bandwidthUSDT = bandwidthTrx.Div(tron.DecimalsTRX).Mul(stx.Price).Mul(tron.DecimalsUSDT).RoundUp(0)
		}
		activateFeeUSDT := decimal.Zero
		if stx.ActivateFee.IsPositive() {
			switch stx.ChainIndex {
			case constant.SolChainIndex:
				decimalsSOL := decimal.NewFromInt(10).Pow(decimal.NewFromInt(9))
				activateFeeUSDT = stx.ActivateFee.Div(decimalsSOL).Mul(stx.Price).Mul(tron.DecimalsUSDT).RoundUp(0)
			case constant.TronChainIndex:
				activateFeeUSDT = stx.ActivateFee.Div(tron.DecimalsTRX).Mul(stx.Price).Mul(tron.DecimalsUSDT).RoundUp(0)
			}
		}

		nativeDecimals, err := constant.NativeDecimals(stx.ChainIndex)
		if err != nil {
			return nil, fmt.Errorf("native decimals error: %w", err)
		}

		nativeTxValueUSDT := decimal.Zero
		if stx.NativeTxValue.IsPositive() {
			nativeTxValueUSDT = stx.NativeTxValue.Div(nativeDecimals).Mul(stx.Price).Mul(tron.DecimalsUSDT).RoundUp(0)
		}

		nativeTxGasUSDT := decimal.Zero
		if stx.NativeTxGas.IsPositive() {
			nativeTxGasUSDT = stx.NativeTxGas.Div(nativeDecimals).Mul(stx.Price).Mul(tron.DecimalsUSDT).RoundUp(0)
		}

		gp := &pb.TxRefGasPool{
			Gas:             stx.Gas.String(),
			GasUsdt:         stx.GasUSDT.String(),
			Status:          status,
			Price:           stx.Price.String(),
			ActivateFee:     stx.ActivateFee.String(),
			ActivateFeeUsdt: activateFeeUSDT.String(),
			Energy:          stx.Energy.String(),
			EnergyTrx:       energyTrx.String(),
			EnergyUsdt:      energyUSDT.String(),
			Bandwidth:       stx.Bandwidth.String(),
			BandwidthTrx:    bandwidthTrx.String(),
			BandwidthUsdt:   bandwidthUSDT.String(),
			BandwidthPrice:  stx.BandwidthPrice.String(),

			NativeTxHash:      stx.NativeTxHash,
			NativeTxValue:     stx.NativeTxValue.String(),
			NativeTxValueUsdt: nativeTxValueUSDT.String(),
			NativeTxGas:       stx.NativeTxGas.String(),
			NativeTxGasUsdt:   nativeTxGasUSDT.String(),
		}
		// token
		ta, err := s.taUc.FindTokenAssetByChainIndexAndAddress(ctx, stx.ChainIndex, stx.Contract)
		if err != nil {
			return nil, err
		}
		gp.Token = &pb.TxRefToken{
			Name:       ta.Name,
			Symbol:     ta.Symbol,
			Decimals:   ta.Decimals,
			Address:    ta.Address,
			ChainIndex: ta.ChainIndex,
			LogoUrl:    ta.LogoUrl,
		}
		txDetail.Gaspool = gp
		// status is success, but tx is nil
		if gpIsSuccess && tx == nil {
			var method string
			switch stx.TxType {
			case model.GasPoolTxTypeTransfer,
				model.GasPoolTxTypeDeposit,
				model.GasPoolTxTypeDepositPreReduceGas:
				method = constant.TxMethodTransfer
			case model.GasPoolTxTypeSwapApproval:
				method = constant.TxMethodApproval
			}
			tx = &model.Transaction{
				TxHash:      stx.TxHash,
				FromAddress: stx.From,
				ToAddress:   stx.To,
				Value:       stx.Value.String(),
				Fee:         stx.Gas.String(),
				Method:      method,
				ProgramID:   stx.Contract,
				Timestamp:   stx.CreatedAt.Unix(),
				Status:      constant.TransactionStatusPending,
				ChainIndex:  stx.ChainIndex,
			}
		}
	}
	if tx != nil {
		txDetail.Txn = tx.TxHash
		txDetail.FromAddress = tx.FromAddress
		txDetail.ToAddress = tx.ToAddress
		txDetail.Value = tx.Value
		txDetail.Fee = tx.Fee
		txDetail.Method = tx.Method
		txDetail.ProgramId = tx.ProgramID
		txDetail.Timestamp = tx.Timestamp
		txDetail.Status = tx.Status
		txDetail.BlockNumber = tx.BlockNumber
		txDetail.ChainIndex = tx.ChainIndex

	}
	if stx != nil && txDetail.Value != stx.Value.String() {
		txDetail.Value = stx.Value.String()
		txDetail.FromAddress = stx.From
		txDetail.ToAddress = stx.To
	}

	return txDetail, nil
}

func (s *WalletSrvService) TransactionsByAddress(ctx context.Context, req *pb.TransactionsByAddressReq) (*pb.TransactionsReply, error) {
	list, total, err := s.uc.TransactionsByAddress(req)
	if err != nil {
		return nil, err
	}
	var transactions []*pb.Transaction
	for _, v := range list {
		var transaction pb.Transaction
		transaction.Txn = v.TxHash
		transaction.ChainIndex = v.ChainIndex
		transaction.FromAddress = v.FromAddress
		transaction.ToAddress = v.ToAddress
		transaction.Value = v.Value
		transaction.ProgramId = v.ProgramID
		transaction.Fee = v.Fee
		transaction.Timestamp = v.Timestamp
		transaction.Method = v.Method
		transaction.Status = v.Status
		transaction.BlockNumber = v.BlockNumber
		transactions = append(transactions, &transaction)
	}
	return &pb.TransactionsReply{
		List:  transactions,
		Count: total,
	}, nil
}

func (s *WalletSrvService) ReportInternalTxn(ctx context.Context, req *pb.ReportInternalTxnReq) (*emptypb.Empty, error) {
	err := s.uc.ReportInternalTxn(req)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}
