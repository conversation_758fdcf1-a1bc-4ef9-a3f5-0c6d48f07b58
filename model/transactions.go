package model

import (
	"byd_wallet/common/constant"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// Transaction 交易记录
type Transaction struct {
	gorm.Model
	TxHash       string `gorm:"index:,unique,composite:from_to_hash_pid;type:varchar(100);comment:交易哈希"`
	BlockNumber  int64  `gorm:"comment:EVM 区块高度"`
	ChainIndex   int64  `gorm:"comment:bip44 chainIndex"`
	FromAddress  string `gorm:"index:,unique,composite:from_to_hash_pid;type:varchar(100);comment:发送方地址"`
	ToAddress    string `gorm:"index:,unique,composite:from_to_hash_pid;type:varchar(100);comment:接收方地址"`
	Value        string `gorm:"type:decimal(100,0);comment:交易金额"`
	Fee          string `gorm:"type:decimal(38,0);comment:交易手续费"`
	Method       string `gorm:"type:varchar(100);comment:EVM 调用方法，如 transfer"`
	ProgramID    string `gorm:"index:,unique,composite:from_to_hash_pid;type:citext;comment:<PERSON>ana 程序 ID/合约地址"`
	Status       string `gorm:"type:varchar(20);comment:交易状态，如 success、fail"`
	Timestamp    int64  `gorm:"index;comment:交易时间戳unix"`
	TokenDecimal int64  `gorm:"comment:token精度"`
}

type TransactionView struct {
	Transaction
	TokenName     string
	TokenSymbol   string
	TokenDecimals int64
}

func (t *Transaction) TxAt() *time.Time {
	if t.Timestamp == 0 {
		return nil
	}
	txAt := time.Unix(t.Timestamp, 0)
	return &txAt
}

func (t *Transaction) Equal(t2 *Transaction) bool {
	return t.TxHash == t2.TxHash && t.FromAddress == t2.FromAddress && t.ToAddress == t2.ToAddress && t.ProgramID == t2.ProgramID
}

func (t *Transaction) IsCreateToken() bool {
	if t.ProgramID == "" {
		return false
	}
	if t.ChainIndex == constant.SolChainIndex {
		return t.Method == constant.InitializeMintMethod
	}
	return t.Method == constant.TxMethodCreated
}

func (t *Transaction) IsHoldNewToken() bool {
	if t.ProgramID == "" {
		switch t.ChainIndex {
		case constant.BaseChainIndex,
			constant.OptimismChainIndex,
			constant.ArbChainIndex,
			constant.PolChainIndex:
		default:
			// skip: has self native coin
			return false
		}
	}
	if t.ChainIndex == constant.SolChainIndex {
		return t.Method == constant.TransferMethod ||
			t.Method == constant.TransferCheckedMethod ||
			t.Method == constant.MintToMethod ||
			t.Method == constant.InstructionWithdrawNonceAccountInstructionMethod ||
			t.Method == constant.InstructionCreateAccountMethod ||
			t.Method == constant.CloseAccountMethod
	}
	return t.Method == constant.TxMethodTransfer
}

func (t *Transaction) IsInitAccount() bool {
	return t.Method == constant.InstructionInitializeAccountMethod || t.Method == constant.InstructionInitializeAccount2Method || t.Method == constant.InstructionInitializeAccount3Method
}

var chainIndex2TxDbNameSuffix = map[int64]string{
	constant.BtcChainIndex:      "bitcoin",
	constant.EthChainIndex:      "ethereum",
	constant.SolChainIndex:      "solana",
	constant.BscChainIndex:      "binance",
	constant.PolChainIndex:      "polygon",
	constant.BaseChainIndex:     "base",
	constant.ArbChainIndex:      "arbitrum",
	constant.OptimismChainIndex: "optimism",
	constant.TronChainIndex:     "tron",
}

func (*Transaction) TableName(chainIndex int64) string {
	tn, ok := chainIndex2TxDbNameSuffix[chainIndex]
	if !ok {
		return ""
	}
	return fmt.Sprintf("transactions_%s", tn)
}
