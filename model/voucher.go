package model

import (
	"gorm.io/gorm"
	"time"
)

type VoucherStatus string

const (
	VoucherStatusInit    VoucherStatus = "init"
	VoucherStatusPending VoucherStatus = "pending"
	VoucherStatusSuccess VoucherStatus = "success"
	VoucherStatusFail    VoucherStatus = "fail"
)

// VoucherRecord 兑换券记录
type VoucherRecord struct {
	gorm.Model
	UserID       uint          `gorm:"index;comment:用户id"`
	Nickname     string        `gorm:"comment:昵称"`
	Code         string        `gorm:"uniqueIndex;comment:兑换码"`
	VoucherValue string        `gorm:"type:decimal(100,0);comment:兑换券面值"`
	TokenAssetID uint          `gorm:"index;comment:TokenAssetID"`
	TokenAsset   *TokenAsset   `gorm:"foreignKey:TokenAssetID;references:ID"`
	FromAddress  string        `gorm:"comment:转出地址"`
	ToAddress    string        `gorm:"comment:转入地址"`
	Value        string        `gorm:"type:decimal(100,0);comment:转账数量"`
	TxHash       string        `gorm:"index;comment:交易hash"`
	Method       string        `gorm:"comment:交易类型"`
	Status       VoucherStatus `gorm:"index;comment:兑换状态"`
	FinishedAt   *time.Time    `gorm:"comment:交易时间"`
	ErrMsg       string        `gorm:"comment:错误信息"`
	Dryrun       bool          `gorm:"comment:是否是测试"`
	ChainIndex   int64         `gorm:"comment:链索引"`
	ChainName    string        `gorm:"comment:链名称"`
	Symbol       string        `gorm:"comment:币种"`
	TokenAddress string        `gorm:"comment:合约地址"`
	Decimals     int64         `gorm:"comment:精度"`
}

func (vr VoucherRecord) Timestamp() int64 {
	timestamp := vr.CreatedAt.Unix()
	if vr.FinishedAt != nil {
		timestamp = vr.FinishedAt.Unix()
	}
	return timestamp
}

type VoucherConfig struct {
	gorm.Model
	ChainIndex      int64  `gorm:"uniqueIndex;comment:链索引"`
	PlatformAddress string `gorm:"comment:平台地址"`
	Dryrun          bool   `gorm:"comment:是否是测试"`
}
