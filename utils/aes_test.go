package utils

import (
	"encoding/hex"
	"fmt"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestEncryptAndDecryptWithAESCBC(t *testing.T) {
	key, _ := hex.DecodeString("6368616e676520746869732070617373")
	plaintext := []byte("123456")
	ciphertext, err := EncryptWithAESCBC(plaintext, key)
	assert.NoError(t, err)
	plaintextB, err := DecryptWithAESCBC(ciphertext, key)
	assert.NoError(t, err)
	fmt.Println(string(plaintextB))
	assert.Equal(t, plaintext, plaintextB)
}

func TestDecryptWithAESCBC(t *testing.T) {
	cipherHex := "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"
	key := []byte("aqW07LykZ6TCceW1vfq07ws9XQKjbfNH")
	ciphertext, err := DecryptWithAESCBC(cipherHex, key)
	assert.NoError(t, err)
	t.Log(string(ciphertext))
}
