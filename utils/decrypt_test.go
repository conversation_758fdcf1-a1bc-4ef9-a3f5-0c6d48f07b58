package utils

import (
	"encoding/hex"
	"fmt"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestEncryptWithAESGCM(t *testing.T) {
	keyStr, _ := generateAESKeyBase64(32) // 256-bit key
	fmt.Println("🔐 keyStr:", keyStr)
	key, _ := decodeAESKeyBase64(keyStr)
	cipherText, _ := EncryptWithAESGCM([]byte("Lza34K2seAZ7zRQABzXu5G1wcAJL9sbs4VMN2caf69gqUyChkVshvc76FzwQCyziadb83BD6LCLkdRF6UcLCSAV"), key)
	plainText, _ := DecryptWithAESGCM(cipherText, keyStr)

	fmt.Println("🔐 keyStr:", keyStr)
	fmt.Println("🔐 Encrypted:", cipherText)
	fmt.Println("🔓 Decrypted:", string(plainText))
}

func TestGenerateAESKey(t *testing.T) {
	keyb, _, err := generateAESKey(32)
	assert.NoError(t, err)
	t.Log(hex.EncodeToString(keyb))
}
