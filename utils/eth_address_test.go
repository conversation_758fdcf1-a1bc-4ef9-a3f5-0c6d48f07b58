package utils

import (
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestGenerateAddress(t *testing.T) {
	privateKey, err := crypto.GenerateKey()
	assert.NoError(t, err)
	t.Log(hexutil.Encode(crypto.FromECDSA(privateKey)))

	address, pk, err := GenerateAddress()
	assert.NoError(t, err)
	t.Log(address)
	t.Log(pk)
}
