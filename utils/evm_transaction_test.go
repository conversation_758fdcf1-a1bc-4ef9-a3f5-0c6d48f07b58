package utils

import (
	"encoding/hex"
	"math/big"
	"testing"
)

func TestRlpDecodeBytes(t *testing.T) {
	rawTx := "0xf869178312561082627094f3e992cbc3ef97870762cfcaf5240bd06a5ee14a85b0f387b0008082422ea0da4de48ab6a3e86927e638b70bed236126c5e997d731b3d294314e0b0dd8e788a0504a408055ecd842c45ad95a7af2f2b97c36529d9ae879a53922411f4e29bf43"
	tx, err := RlpDecodeBytes(rawTx)
	if err != nil {
		t.<PERSON>rror(err.<PERSON>rror())
	}
	t.Log("tx = ", tx)
	if tx == nil {
		t.Fatal()
	}
	t.Log("ChainId = ", tx.ChainId())
	t.Log("Value = ", tx.Value())
	t.Log("Data = ", hex.EncodeToString(tx.Data()))
	t.Log("Gas = ", tx.Gas())
	t.Log("GasPrice = ", tx.GasPrice())
	t.Log("To = ", tx.To())
	t.Logf("Nonce: %d", tx.Nonce())
	t.Logf("Fee: %d", tx.GasPrice().Mul(big.NewInt(int64(tx.Gas())), tx.GasPrice()))
	from, err := GetTxSender(tx)
	if err != nil {
		t.<PERSON>("GetSenderFromTransaction err ", err.Error())
	}
	t.Log("from", from)

}
